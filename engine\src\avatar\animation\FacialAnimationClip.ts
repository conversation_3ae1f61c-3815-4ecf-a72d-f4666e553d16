/**
 * 面部动画片段
 * 用于存储和管理面部动画数据
 */
import { FacialExpressionType, VisemeType } from '../components/FacialAnimationComponent';

/**
 * 面部动画关键帧
 */
export interface FacialAnimationKeyframe {
  /** 时间（秒） */
  time: number;
  /** 值 */
  value: any;
  /** 权重 */
  weight: number;
  /** 插值类型 */
  interpolation?: 'linear' | 'step' | 'cubic';
  /** 进入切线 */
  inTangent?: number;
  /** 退出切线 */
  outTangent?: number;
}

/**
 * 表情关键帧
 */
export interface ExpressionKeyframe extends FacialAnimationKeyframe {
  /** 表情类型 */
  expression: FacialExpressionType;
}

/**
 * 口型关键帧
 */
export interface VisemeKeyframe extends FacialAnimationKeyframe {
  /** 口型类型 */
  viseme: VisemeType;
}

/**
 * 面部动画片段
 */
export class FacialAnimationClip {
  /** 名称 */
  public name: string;

  /** 持续时间（秒） */
  public duration: number = 5.0;

  /** 是否循环 */
  public loop: boolean = false;

  /** 表情关键帧 */
  private expressionKeyframes: ExpressionKeyframe[] = [];

  /** 口型关键帧 */
  private visemeKeyframes: VisemeKeyframe[] = [];

  /** 自定义数据 */
  private userData: any = {};

  /**
   * 构造函数
   * @param name 名称
   */
  constructor(name: string) {
    this.name = name;
  }

  /**
   * 添加表情关键帧
   * @param time 时间（秒）
   * @param expression 表情类型
   * @param weight 权重
   * @param interpolation 插值类型
   * @returns 关键帧索引
   */
  public addExpressionKeyframe(
    time: number,
    expression: FacialExpressionType,
    weight: number = 1.0,
    interpolation: 'linear' | 'step' | 'cubic' = 'linear'
  ): number {
    const keyframe: ExpressionKeyframe = {
      time,
      value: expression,
      expression,
      weight,
      interpolation
    };

    // 插入关键帧（按时间排序）
    let index = 0;
    while (index < this.expressionKeyframes.length && this.expressionKeyframes[index].time < time) {
      index++;
    }

    this.expressionKeyframes.splice(index, 0, keyframe);
    return index;
  }

  /**
   * 添加口型关键帧
   * @param time 时间（秒）
   * @param viseme 口型类型
   * @param weight 权重
   * @param interpolation 插值类型
   * @returns 关键帧索引
   */
  public addVisemeKeyframe(
    time: number,
    viseme: VisemeType,
    weight: number = 1.0,
    interpolation: 'linear' | 'step' | 'cubic' = 'step'
  ): number {
    const keyframe: VisemeKeyframe = {
      time,
      value: viseme,
      viseme,
      weight,
      interpolation
    };

    // 插入关键帧（按时间排序）
    let index = 0;
    while (index < this.visemeKeyframes.length && this.visemeKeyframes[index].time < time) {
      index++;
    }

    this.visemeKeyframes.splice(index, 0, keyframe);
    return index;
  }

  /**
   * 移除表情关键帧
   * @param index 索引
   * @returns 是否成功移除
   */
  public removeExpressionKeyframe(index: number): boolean {
    if (index >= 0 && index < this.expressionKeyframes.length) {
      this.expressionKeyframes.splice(index, 1);
      return true;
    }
    return false;
  }

  /**
   * 移除口型关键帧
   * @param index 索引
   * @returns 是否成功移除
   */
  public removeVisemeKeyframe(index: number): boolean {
    if (index >= 0 && index < this.visemeKeyframes.length) {
      this.visemeKeyframes.splice(index, 1);
      return true;
    }
    return false;
  }

  /**
   * 获取表情关键帧
   * @param index 索引
   * @returns 表情关键帧
   */
  public getExpressionKeyframe(index: number): ExpressionKeyframe | null {
    if (index >= 0 && index < this.expressionKeyframes.length) {
      return this.expressionKeyframes[index];
    }
    return null;
  }

  /**
   * 获取口型关键帧
   * @param index 索引
   * @returns 口型关键帧
   */
  public getVisemeKeyframe(index: number): VisemeKeyframe | null {
    if (index >= 0 && index < this.visemeKeyframes.length) {
      return this.visemeKeyframes[index];
    }
    return null;
  }

  /**
   * 获取所有表情关键帧
   * @returns 表情关键帧数组
   */
  public getAllExpressionKeyframes(): ExpressionKeyframe[] {
    return [...this.expressionKeyframes];
  }

  /**
   * 获取所有口型关键帧
   * @returns 口型关键帧数组
   */
  public getAllVisemeKeyframes(): VisemeKeyframe[] {
    return [...this.visemeKeyframes];
  }

  /**
   * 在指定时间获取表情
   * @param time 时间（秒）
   * @returns 表情和权重
   */
  public getExpressionAtTime(time: number): { expression: FacialExpressionType, weight: number } {
    // 如果没有关键帧，返回默认值
    if (this.expressionKeyframes.length === 0) {
      return { expression: FacialExpressionType.NEUTRAL, weight: 1.0 };
    }

    // 如果只有一个关键帧，返回该关键帧的值
    if (this.expressionKeyframes.length === 1) {
      return {
        expression: this.expressionKeyframes[0].expression,
        weight: this.expressionKeyframes[0].weight
      };
    }

    // 如果时间小于第一个关键帧的时间，返回第一个关键帧的值
    if (time <= this.expressionKeyframes[0].time) {
      return {
        expression: this.expressionKeyframes[0].expression,
        weight: this.expressionKeyframes[0].weight
      };
    }

    // 如果时间大于最后一个关键帧的时间，返回最后一个关键帧的值
    if (time >= this.expressionKeyframes[this.expressionKeyframes.length - 1].time) {
      const lastKeyframe = this.expressionKeyframes[this.expressionKeyframes.length - 1];
      return {
        expression: lastKeyframe.expression,
        weight: lastKeyframe.weight
      };
    }

    // 找到时间所在的两个关键帧
    let index = 0;
    while (index < this.expressionKeyframes.length - 1 && this.expressionKeyframes[index + 1].time < time) {
      index++;
    }

    const keyframe1 = this.expressionKeyframes[index];
    const keyframe2 = this.expressionKeyframes[index + 1];

    // 如果是步进插值，返回第一个关键帧的值
    if (keyframe1.interpolation === 'step') {
      return {
        expression: keyframe1.expression,
        weight: keyframe1.weight
      };
    }

    // 计算插值因子
    const t = (time - keyframe1.time) / (keyframe2.time - keyframe1.time);

    // 如果两个关键帧的表情相同，只插值权重
    if (keyframe1.expression === keyframe2.expression) {
      return {
        expression: keyframe1.expression,
        weight: keyframe1.weight * (1 - t) + keyframe2.weight * t
      };
    }

    // 如果两个关键帧的表情不同，返回权重较大的表情
    return {
      expression: t < 0.5 ? keyframe1.expression : keyframe2.expression,
      weight: t < 0.5 ? keyframe1.weight : keyframe2.weight
    };
  }

  /**
   * 在指定时间获取口型
   * @param time 时间（秒）
   * @returns 口型和权重
   */
  public getVisemeAtTime(time: number): { viseme: VisemeType, weight: number } {
    // 如果没有关键帧，返回默认值
    if (this.visemeKeyframes.length === 0) {
      return { viseme: VisemeType.SILENT, weight: 0.0 };
    }

    // 如果只有一个关键帧，返回该关键帧的值
    if (this.visemeKeyframes.length === 1) {
      return {
        viseme: this.visemeKeyframes[0].viseme,
        weight: this.visemeKeyframes[0].weight
      };
    }

    // 如果时间小于第一个关键帧的时间，返回第一个关键帧的值
    if (time <= this.visemeKeyframes[0].time) {
      return {
        viseme: this.visemeKeyframes[0].viseme,
        weight: this.visemeKeyframes[0].weight
      };
    }

    // 如果时间大于最后一个关键帧的时间，返回最后一个关键帧的值
    if (time >= this.visemeKeyframes[this.visemeKeyframes.length - 1].time) {
      const lastKeyframe = this.visemeKeyframes[this.visemeKeyframes.length - 1];
      return {
        viseme: lastKeyframe.viseme,
        weight: lastKeyframe.weight
      };
    }

    // 找到时间所在的两个关键帧
    let index = 0;
    while (index < this.visemeKeyframes.length - 1 && this.visemeKeyframes[index + 1].time < time) {
      index++;
    }

    const keyframe1 = this.visemeKeyframes[index];
    const keyframe2 = this.visemeKeyframes[index + 1];

    // 如果是步进插值，返回第一个关键帧的值
    if (keyframe1.interpolation === 'step') {
      return {
        viseme: keyframe1.viseme,
        weight: keyframe1.weight
      };
    }

    // 计算插值因子
    const t = (time - keyframe1.time) / (keyframe2.time - keyframe1.time);

    // 如果两个关键帧的口型相同，只插值权重
    if (keyframe1.viseme === keyframe2.viseme) {
      return {
        viseme: keyframe1.viseme,
        weight: keyframe1.weight * (1 - t) + keyframe2.weight * t
      };
    }

    // 如果两个关键帧的口型不同，返回权重较大的口型
    return {
      viseme: t < 0.5 ? keyframe1.viseme : keyframe2.viseme,
      weight: t < 0.5 ? keyframe1.weight : keyframe2.weight
    };
  }

  /**
   * 设置自定义数据
   * @param key 键
   * @param value 值
   */
  public setUserData(key: string, value: any): void {
    this.userData[key] = value;
  }

  /**
   * 获取自定义数据
   * @param key 键
   * @returns 值
   */
  public getUserData(key: string): any {
    return this.userData[key];
  }

  /**
   * 清除所有关键帧
   */
  public clear(): void {
    this.expressionKeyframes = [];
    this.visemeKeyframes = [];
  }

  /**
   * 克隆
   * @returns 克隆的面部动画片段
   */
  public clone(): FacialAnimationClip {
    const clip = new FacialAnimationClip(this.name);
    clip.duration = this.duration;
    clip.loop = this.loop;
    clip.expressionKeyframes = [...this.expressionKeyframes];
    clip.visemeKeyframes = [...this.visemeKeyframes];
    clip.userData = { ...this.userData };
    return clip;
  }

  /**
   * 转换为JSON
   * @returns JSON对象
   */
  public toJSON(): any {
    return {
      name: this.name,
      duration: this.duration,
      loop: this.loop,
      expressionKeyframes: this.expressionKeyframes,
      visemeKeyframes: this.visemeKeyframes,
      userData: this.userData
    };
  }

  /**
   * 从JSON创建
   * @param json JSON对象
   * @returns 面部动画片段
   */
  public static fromJSON(json: any): FacialAnimationClip {
    const clip = new FacialAnimationClip(json.name);
    clip.duration = json.duration;
    clip.loop = json.loop;
    clip.expressionKeyframes = json.expressionKeyframes;
    clip.visemeKeyframes = json.visemeKeyframes;
    clip.userData = json.userData;
    return clip;
  }
}
