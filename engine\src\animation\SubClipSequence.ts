/**
 * 子片段序列
 * 用于按顺序播放多个子片段
 */
import * as THREE from 'three';
import { EventEmitter } from '../utils/EventEmitter';
import { SubClip } from './SubClip';
import { AnimationSubClip } from './AnimationSubClip';

/**
 * 子片段序列事件类型
 */
export enum SubClipSequenceEventType {
  /** 序列开始 */
  SEQUENCE_START = 'sequenceStart',
  /** 序列结束 */
  SEQUENCE_END = 'sequenceEnd',
  /** 子片段开始 */
  CLIP_START = 'clipStart',
  /** 子片段结束 */
  CLIP_END = 'clipEnd',
  /** 序列更新 */
  SEQUENCE_UPDATE = 'sequenceUpdate',
  /** 序列循环 */
  SEQUENCE_LOOP = 'sequenceLoop'
}

/**
 * 子片段序列项
 */
export interface SubClipSequenceItem {
  /** 子片段 */
  subClip: SubClip | AnimationSubClip;
  /** 持续时间（秒） */
  duration: number;
  /** 过渡时间（秒） */
  transitionTime: number;
  /** 权重 */
  weight: number;
  /** 自定义数据 */
  userData?: any;
}

/**
 * 子片段序列配置
 */
export interface SubClipSequenceConfig {
  /** 序列名称 */
  name?: string;
  /** 是否循环 */
  loop?: boolean;
  /** 是否自动播放 */
  autoPlay?: boolean;
  /** 是否启用调试 */
  debug?: boolean;
}

/**
 * 子片段序列
 */
export class SubClipSequence {
  /** 序列名称 */
  private name: string;
  /** 是否循环 */
  private loop: boolean;
  /** 是否自动播放 */
  private autoPlay: boolean;
  /** 是否启用调试 */
  private debug: boolean;
  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();
  /** 序列项列表 */
  private items: SubClipSequenceItem[] = [];
  /** 当前索引 */
  private currentIndex: number = -1;
  /** 是否正在播放 */
  private isPlaying: boolean = false;
  /** 当前时间 */
  private currentTime: number = 0;
  /** 当前项的开始时间 */
  private currentItemStartTime: number = 0;
  /** 总持续时间 */
  private totalDuration: number = 0;
  /** 当前动作 */
  private currentAction: THREE.AnimationAction | null = null;
  /** 下一个动作 */
  private nextAction: THREE.AnimationAction | null = null;

  /**
   * 创建子片段序列
   * @param config 配置
   */
  constructor(config: SubClipSequenceConfig = {}) {
    this.name = config.name || 'sequence';
    this.loop = config.loop !== undefined ? config.loop : false;
    this.autoPlay = config.autoPlay !== undefined ? config.autoPlay : false;
    this.debug = config.debug !== undefined ? config.debug : false;
  }

  /**
   * 获取序列名称
   * @returns 序列名称
   */
  public getName(): string {
    return this.name;
  }

  /**
   * 设置序列名称
   * @param name 序列名称
   */
  public setName(name: string): void {
    this.name = name;
  }

  /**
   * 获取是否循环
   * @returns 是否循环
   */
  public getLoop(): boolean {
    return this.loop;
  }

  /**
   * 设置是否循环
   * @param loop 是否循环
   */
  public setLoop(loop: boolean): void {
    this.loop = loop;
  }

  /**
   * 获取是否自动播放
   * @returns 是否自动播放
   */
  public getAutoPlay(): boolean {
    return this.autoPlay;
  }

  /**
   * 设置是否自动播放
   * @param autoPlay 是否自动播放
   */
  public setAutoPlay(autoPlay: boolean): void {
    this.autoPlay = autoPlay;
  }

  /**
   * 添加子片段
   * @param subClip 子片段
   * @param duration 持续时间（秒）
   * @param transitionTime 过渡时间（秒）
   * @param weight 权重
   * @param userData 自定义数据
   * @returns 序列项索引
   */
  public addSubClip(
    subClip: SubClip | AnimationSubClip,
    duration: number = 0,
    transitionTime: number = 0.5,
    weight: number = 1.0,
    userData?: any
  ): number {
    // 创建序列项
    const item: SubClipSequenceItem = {
      subClip,
      duration: duration > 0 ? duration : (subClip instanceof SubClip ? subClip.getDuration() : 0),
      transitionTime,
      weight,
      userData
    };

    // 添加到列表
    this.items.push(item);

    // 更新总持续时间
    this.updateTotalDuration();

    // 如果是第一个项且自动播放，则开始播放
    if (this.items.length === 1 && this.autoPlay && !this.isPlaying) {
      this.play();
    }

    // 触发序列更新事件
    this.eventEmitter.emit(SubClipSequenceEventType.SEQUENCE_UPDATE, {
      sequence: this,
      items: this.items
    });

    if (this.debug) {
      console.log(`添加子片段到序列: ${this.name}, 子片段: ${subClip.getName()}, 持续时间: ${item.duration}, 过渡时间: ${transitionTime}`);
    }

    return this.items.length - 1;
  }

  /**
   * 移除子片段
   * @param index 序列项索引
   */
  public removeSubClip(index: number): void {
    if (index < 0 || index >= this.items.length) return;

    // 移除项
    this.items.splice(index, 1);

    // 更新总持续时间
    this.updateTotalDuration();

    // 如果正在播放且移除的是当前项，则停止播放
    if (this.isPlaying && index === this.currentIndex) {
      this.stop();
    }
    // 如果移除的项在当前项之前，则更新当前索引
    else if (this.isPlaying && index < this.currentIndex) {
      this.currentIndex--;
    }

    // 触发序列更新事件
    this.eventEmitter.emit(SubClipSequenceEventType.SEQUENCE_UPDATE, {
      sequence: this,
      items: this.items
    });

    if (this.debug) {
      console.log(`从序列移除子片段: ${this.name}, 索引: ${index}`);
    }
  }

  /**
   * 清空序列
   */
  public clear(): void {
    // 停止播放
    this.stop();

    // 清空列表
    this.items = [];

    // 重置状态
    this.currentIndex = -1;
    this.currentTime = 0;
    this.currentItemStartTime = 0;
    this.totalDuration = 0;

    // 触发序列更新事件
    this.eventEmitter.emit(SubClipSequenceEventType.SEQUENCE_UPDATE, {
      sequence: this,
      items: this.items
    });

    if (this.debug) {
      console.log(`清空序列: ${this.name}`);
    }
  }

  /**
   * 获取序列项列表
   * @returns 序列项列表
   */
  public getItems(): SubClipSequenceItem[] {
    return [...this.items];
  }

  /**
   * 获取序列项数量
   * @returns 序列项数量
   */
  public getItemCount(): number {
    return this.items.length;
  }

  /**
   * 获取当前索引
   * @returns 当前索引
   */
  public getCurrentIndex(): number {
    return this.currentIndex;
  }

  /**
   * 获取当前项
   * @returns 当前项
   */
  public getCurrentItem(): SubClipSequenceItem | null {
    if (this.currentIndex < 0 || this.currentIndex >= this.items.length) return null;
    return this.items[this.currentIndex];
  }

  /**
   * 获取总持续时间
   * @returns 总持续时间（秒）
   */
  public getTotalDuration(): number {
    return this.totalDuration;
  }

  /**
   * 更新总持续时间
   */
  private updateTotalDuration(): void {
    this.totalDuration = this.items.reduce((total, item) => total + item.duration, 0);
  }

  /**
   * 添加事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public addEventListener(event: SubClipSequenceEventType, listener: (data: any) => void): void {
    this.eventEmitter.on(event, listener);
  }

  /**
   * 移除事件监听器
   * @param event 事件类型
   * @param listener 监听器
   */
  public removeEventListener(event: SubClipSequenceEventType, listener: (data: any) => void): void {
    this.eventEmitter.off(event, listener);
  }
}
