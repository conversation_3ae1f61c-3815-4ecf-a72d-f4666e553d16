/**
 * 情感动画生成器
 * 用于基于情感分析生成面部动画
 */
import { FacialExpressionType, VisemeType } from '../components/FacialAnimationComponent';
import { AnimationGenerationRequest, AnimationGenerationResult } from './AnimationGenerationTypes';
import { AIModel } from './AIModel';
import { FacialAnimationClip, FacialAnimationKeyframe } from '../animation/FacialAnimationClip';

/**
 * 情感动画生成器配置
 */
export interface EmotionBasedAnimationGeneratorConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 是否使用本地模型 */
  useLocalModel?: boolean;
  /** 模型路径 */
  modelPath?: string;
  /** 是否使用GPU */
  useGPU?: boolean;
  /** 模型类型 */
  modelType?: 'bert' | 'roberta' | 'distilbert' | 'albert' | 'xlnet' | 'custom';
  /** 模型变体 */
  modelVariant?: string;
  /** 是否使用量化模型 */
  useQuantized?: boolean;
  /** 量化位数 */
  quantizationBits?: 8 | 16 | 32;
  /** 批处理大小 */
  batchSize?: number;
  /** 情感类别 */
  emotionCategories?: string[];
  /** 是否使用缓存 */
  useCache?: boolean;
  /** 缓存大小 */
  cacheSize?: number;
}

/**
 * 情感分析结果
 */
export interface EmotionAnalysisResult {
  /** 主要情感 */
  primaryEmotion: string;
  /** 主要情感强度 */
  primaryIntensity: number;
  /** 次要情感 */
  secondaryEmotion?: string;
  /** 次要情感强度 */
  secondaryIntensity?: number;
  /** 情感变化 */
  emotionChanges?: { emotion: string, intensity: number, time: number }[];
  /** 情感持续时间 */
  emotionDuration?: number;
}

/**
 * 情感到表情的映射
 */
const EMOTION_TO_EXPRESSION_MAP: { [key: string]: FacialExpressionType } = {
  'happy': FacialExpressionType.HAPPY,
  'sad': FacialExpressionType.SAD,
  'angry': FacialExpressionType.ANGRY,
  'surprised': FacialExpressionType.SURPRISED,
  'fear': FacialExpressionType.FEARFUL,
  'disgust': FacialExpressionType.DISGUSTED,
  'neutral': FacialExpressionType.NEUTRAL,
  'joy': FacialExpressionType.HAPPY,
  'sorrow': FacialExpressionType.SAD,
  'rage': FacialExpressionType.ANGRY,
  'shock': FacialExpressionType.SURPRISED,
  'terror': FacialExpressionType.FEARFUL,
  'contempt': FacialExpressionType.CONTEMPT
};

/**
 * 情感动画生成器
 */
export class EmotionBasedAnimationGenerator {
  /** 配置 */
  private config: EmotionBasedAnimationGeneratorConfig;

  /** AI模型 */
  private aiModel: AIModel;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /** 是否启用调试 */
  private debug: boolean = false;

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: EmotionBasedAnimationGeneratorConfig = {}) {
    this.config = {
      debug: false,
      useLocalModel: false,
      modelPath: '',
      useGPU: false,
      modelType: 'bert',
      modelVariant: 'base',
      useQuantized: false,
      quantizationBits: 8,
      batchSize: 1,
      useCache: true,
      cacheSize: 100,
      ...config
    };

    this.debug = this.config.debug || false;

    // 创建AI模型
    this.aiModel = new AIModel({
      useLocalModel: this.config.useLocalModel,
      modelPath: this.config.modelPath,
      useGPU: this.config.useGPU,
      debug: this.debug,
      modelType: this.config.modelType,
      modelVariant: this.config.modelVariant,
      useQuantized: this.config.useQuantized,
      quantizationBits: this.config.quantizationBits,
      batchSize: this.config.batchSize,
      emotionCategories: this.config.emotionCategories,
      useCache: this.config.useCache,
      cacheSize: this.config.cacheSize
    });

    if (this.debug) {
      console.log('情感动画生成器创建');
    }
  }

  /**
   * 初始化
   */
  public async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      // 初始化AI模型
      await this.aiModel.initialize();

      this.initialized = true;

      if (this.debug) {
        console.log('情感动画生成器初始化成功');
      }
    } catch (error) {
      console.error('情感动画生成器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 生成面部动画
   * @param request 生成请求
   * @returns 生成结果
   */
  public async generateFacialAnimation(request: AnimationGenerationRequest): Promise<AnimationGenerationResult> {
    try {
      // 分析文本情感
      const emotionResult = await this.aiModel.analyzeEmotion(request.prompt);

      if (this.debug) {
        console.log('情感分析结果:', emotionResult);
      }

      // 创建面部动画片段
      const clip = await this.createEmotionBasedFacialClip(request, emotionResult);

      return {
        id: request.id,
        success: true,
        clip,
        generationTime: 0,
        userData: request.userData
      };
    } catch (error) {
      if (this.debug) {
        console.error('生成面部动画失败:', error);
      }

      return {
        id: request.id,
        success: false,
        error: error instanceof Error ? error.message : String(error),
        userData: request.userData
      };
    }
  }

  /**
   * 创建基于情感的面部动画片段
   * @param request 请求
   * @param emotionResult 情感分析结果
   * @returns 面部动画片段
   */
  private async createEmotionBasedFacialClip(
    request: AnimationGenerationRequest,
    emotionResult: EmotionAnalysisResult
  ): Promise<FacialAnimationClip> {
    // 创建动画片段
    const clip = new FacialAnimationClip(request.prompt);

    // 设置持续时间
    clip.duration = request.duration;

    // 设置循环
    clip.loop = request.loop || false;

    // 获取主要表情
    const primaryExpression = this.getExpressionForEmotion(emotionResult.primaryEmotion);

    // 获取次要表情
    const secondaryExpression = emotionResult.secondaryEmotion
      ? this.getExpressionForEmotion(emotionResult.secondaryEmotion)
      : FacialExpressionType.NEUTRAL;

    // 计算强度
    const primaryIntensity = emotionResult.primaryIntensity * (request.intensity || 1.0);
    const secondaryIntensity = (emotionResult.secondaryIntensity || 0) * (request.intensity || 1.0);

    // 如果有情感变化，创建关键帧
    if (emotionResult.emotionChanges && emotionResult.emotionChanges.length > 0) {
      // 添加初始关键帧
      clip.addExpressionKeyframe(0, primaryExpression, primaryIntensity);

      // 添加情感变化关键帧
      for (const change of emotionResult.emotionChanges) {
        const expression = this.getExpressionForEmotion(change.emotion);
        const time = change.time * request.duration;
        const intensity = change.intensity * (request.intensity || 1.0);

        clip.addExpressionKeyframe(time, expression, intensity);
      }

      // 添加结束关键帧
      clip.addExpressionKeyframe(request.duration, primaryExpression, primaryIntensity);
    } else {
      // 创建简单的表情混合
      // 添加初始关键帧
      clip.addExpressionKeyframe(0, FacialExpressionType.NEUTRAL, 1.0);

      // 添加主要表情关键帧
      clip.addExpressionKeyframe(0.5, primaryExpression, primaryIntensity);

      // 如果有次要表情，添加次要表情关键帧
      if (secondaryIntensity > 0.1) {
        clip.addExpressionKeyframe(1.0, secondaryExpression, secondaryIntensity);
        clip.addExpressionKeyframe(1.5, primaryExpression, primaryIntensity);
      }

      // 添加结束关键帧
      clip.addExpressionKeyframe(request.duration, FacialExpressionType.NEUTRAL, 1.0);
    }

    // 如果需要口型同步，添加口型关键帧
    if (request.prompt.length > 0) {
      // 简单的口型同步
      const words = request.prompt.split(' ');
      const wordDuration = request.duration / words.length;

      for (let i = 0; i < words.length; i++) {
        const time = i * wordDuration;
        const viseme = this.getRandomViseme();

        clip.addVisemeKeyframe(time, viseme, 1.0);
        clip.addVisemeKeyframe(time + wordDuration * 0.5, VisemeType.SILENT, 0.2);
      }
    }

    return clip;
  }

  /**
   * 获取情感对应的表情
   * @param emotion 情感
   * @returns 表情
   */
  private getExpressionForEmotion(emotion: string): FacialExpressionType {
    const lowerEmotion = emotion.toLowerCase();
    return EMOTION_TO_EXPRESSION_MAP[lowerEmotion] || FacialExpressionType.NEUTRAL;
  }

  /**
   * 获取随机口型
   * @returns 口型
   */
  private getRandomViseme(): VisemeType {
    const visemes = [
      VisemeType.AA,
      VisemeType.EE,
      VisemeType.IH,
      VisemeType.OH,
      VisemeType.OU,
      VisemeType.MM
    ];

    const index = Math.floor(Math.random() * visemes.length);
    return visemes[index];
  }
}
