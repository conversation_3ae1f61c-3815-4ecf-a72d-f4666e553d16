/**
 * 增强资源预加载器
 * 提供更高效的资源预加载功能
 */
import { EventEmitter } from '../utils/EventEmitter';
import { AssetType } from './ResourceManager';
import { EnhancedResourceManager } from './EnhancedResourceManager';
import { DependencyType, EnhancedResourceDependencyManager } from './EnhancedResourceDependencyManager';

/**
 * 预加载资源信息
 */
export interface PreloadResourceInfo {
  /** 资源ID */
  id: string;
  /** 资源类型 */
  type: AssetType;
  /** 资源URL */
  url: string;
  /** 资源优先级（0-100，数值越大优先级越高） */
  priority?: number;
  /** 资源组 */
  group?: string;
  /** 资源元数据 */
  metadata?: Record<string, any>;
  /** 资源标签 */
  tags?: string[];
}

/**
 * 预加载组信息
 */
export interface PreloadGroupInfo {
  /** 组名 */
  name: string;
  /** 组优先级（0-100，数值越大优先级越高） */
  priority: number;
  /** 组依赖 */
  dependencies?: string[];
  /** 组资源 */
  resources: PreloadResourceInfo[];
  /** 组元数据 */
  metadata?: Record<string, any>;
  /** 组标签 */
  tags?: string[];
}

/**
 * 预加载进度信息
 */
export interface PreloadProgressInfo {
  /** 组名 */
  group: string;
  /** 已加载资源数 */
  loaded: number;
  /** 总资源数 */
  total: number;
  /** 加载进度（0-1） */
  progress: number;
  /** 已加载资源 */
  loadedResources: string[];
  /** 加载失败资源 */
  failedResources: string[];
  /** 当前加载资源 */
  currentResource?: string;
  /** 已用时间（毫秒） */
  elapsedTime?: number;
  /** 估计剩余时间（毫秒） */
  estimatedTimeRemaining?: number;
  /** 加载速度（资源/秒） */
  loadingSpeed?: number;
}

/**
 * 增强资源预加载器选项
 */
export interface EnhancedResourcePreloaderOptions {
  /** 资源管理器 */
  resourceManager: EnhancedResourceManager;
  /** 依赖管理器 */
  dependencyManager?: EnhancedResourceDependencyManager;
  /** 是否自动注册资源 */
  autoRegisterAssets?: boolean;
  /** 是否自动加载依赖组 */
  autoLoadDependencies?: boolean;
  /** 最大并发加载数 */
  maxConcurrentLoads?: number;
  /** 重试次数 */
  retryCount?: number;
  /** 重试延迟（毫秒） */
  retryDelay?: number;
  /** 是否启用进度估计 */
  enableProgressEstimation?: boolean;
  /** 是否启用调试模式 */
  debug?: boolean;
}

/**
 * 增强资源预加载器
 */
export class EnhancedResourcePreloader extends EventEmitter {
  /** 资源管理器 */
  private resourceManager: EnhancedResourceManager;

  /** 依赖管理器 */
  private dependencyManager?: EnhancedResourceDependencyManager;

  /** 是否自动注册资源 */
  private autoRegisterAssets: boolean;

  /** 是否自动加载依赖组 */
  private autoLoadDependencies: boolean;

  /** 最大并发加载数 */
  private maxConcurrentLoads: number;

  /** 当前并发加载数 */
  private currentConcurrentLoads: number = 0;

  /** 重试次数 */
  private retryCount: number;

  /** 重试延迟（毫秒） */
  private retryDelay: number;

  /** 是否启用进度估计 */
  private enableProgressEstimation: boolean;

  /** 是否启用调试模式 */
  private debug: boolean;

  /** 预加载组映射 */
  private groups: Map<string, PreloadGroupInfo> = new Map();

  /** 预加载进度映射 */
  private progress: Map<string, PreloadProgressInfo> = new Map();

  /** 当前加载组 */
  private currentGroup: string | null = null;

  /** 加载队列 */
  private loadQueue: PreloadResourceInfo[] = [];

  /** 是否正在加载 */
  private loading: boolean = false;

  /** 是否暂停 */
  private paused: boolean = false;

  /** 加载开始时间 */
  private loadStartTime: number = 0;

  /** 上次进度更新时间 */
  private lastProgressUpdateTime: number = 0;

  /** 上次加载资源数 */
  private lastLoadedCount: number = 0;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 创建增强资源预加载器实例
   * @param options 预加载器选项
   */
  constructor(options: EnhancedResourcePreloaderOptions) {
    super();

    this.resourceManager = options.resourceManager;
    this.dependencyManager = options.dependencyManager;
    this.autoRegisterAssets = options.autoRegisterAssets !== undefined ? options.autoRegisterAssets : true;
    this.autoLoadDependencies = options.autoLoadDependencies !== undefined ? options.autoLoadDependencies : true;
    this.maxConcurrentLoads = options.maxConcurrentLoads || 4;
    this.retryCount = options.retryCount || 3;
    this.retryDelay = options.retryDelay || 1000;
    this.enableProgressEstimation = options.enableProgressEstimation !== undefined ? options.enableProgressEstimation : true;
    this.debug = options.debug !== undefined ? options.debug : false;
  }

  /**
   * 初始化预加载器
   */
  public initialize(): void {
    if (this.initialized) {
      return;
    }

    this.initialized = true;
    this.emit('initialized');
  }

  /**
   * 添加预加载组
   * @param group 预加载组信息
   * @returns 是否成功添加
   */
  public addGroup(group: PreloadGroupInfo): boolean {
    // 检查组名是否已存在
    if (this.groups.has(group.name)) {
      if (this.debug) {
        console.warn(`[ResourcePreloader] 组名已存在: ${group.name}`);
      }
      return false;
    }

    // 添加组
    this.groups.set(group.name, {
      ...group,
      priority: group.priority || 0,
      dependencies: group.dependencies || [],
      resources: [...group.resources],
      metadata: group.metadata || {},
      tags: group.tags || []
    });

    // 初始化进度信息
    this.progress.set(group.name, {
      group: group.name,
      loaded: 0,
      total: group.resources.length,
      progress: 0,
      loadedResources: [],
      failedResources: []
    });

    // 如果启用自动注册资源，则注册组中的资源
    if (this.autoRegisterAssets) {
      this.registerGroupAssets(group.name);
    }

    // 如果启用依赖管理，则注册组依赖
    if (this.dependencyManager && group.dependencies) {
      for (const depName of group.dependencies) {
        this.dependencyManager.addDependency(
          group.name,
          depName,
          DependencyType.STRONG,
          group.priority
        );
      }
    }

    // 发出组添加事件
    this.emit('groupAdded', group);

    if (this.debug) {
      console.log(`[ResourcePreloader] 添加预加载组: ${group.name}, 资源数: ${group.resources.length}`);
    }

    return true;
  }

  /**
   * 移除预加载组
   * @param name 组名
   * @returns 是否成功移除
   */
  public removeGroup(name: string): boolean {
    // 检查组名是否存在
    if (!this.groups.has(name)) {
      return false;
    }

    // 如果正在加载该组，则无法移除
    if (this.currentGroup === name && this.loading) {
      if (this.debug) {
        console.warn(`[ResourcePreloader] 无法移除正在加载的组: ${name}`);
      }
      return false;
    }

    // 移除组
    this.groups.delete(name);
    this.progress.delete(name);

    // 如果启用依赖管理，则移除组依赖
    if (this.dependencyManager) {
      // 移除该组作为依赖项
      for (const [groupName, groupInfo] of this.groups.entries()) {
        if (groupInfo.dependencies?.includes(name)) {
          this.dependencyManager.removeDependency(groupName, name);
        }
      }
    }

    // 发出组移除事件
    this.emit('groupRemoved', { name });

    if (this.debug) {
      console.log(`[ResourcePreloader] 移除预加载组: ${name}`);
    }

    return true;
  }

  /**
   * 注册组中的资源
   * @param name 组名
   * @returns 是否成功注册
   */
  private registerGroupAssets(name: string): boolean {
    // 检查组名是否存在
    if (!this.groups.has(name)) {
      return false;
    }

    // 获取组信息
    const group = this.groups.get(name)!;

    // 注册每个资源
    for (const resource of group.resources) {
      // 如果启用依赖管理，则添加资源依赖
      if (this.dependencyManager) {
        this.dependencyManager.addDependency(
          name,
          resource.id,
          DependencyType.STRONG,
          resource.priority
        );
      }
    }

    return true;
  }

  /**
   * 重置组进度信息
   * @param name 组名
   */
  private resetProgress(name: string): void {
    // 检查组名是否存在
    if (!this.groups.has(name) || !this.progress.has(name)) {
      return;
    }

    // 重置进度信息
    this.progress.set(name, {
      group: name,
      loaded: 0,
      total: this.groups.get(name)!.resources.length,
      progress: 0,
      loadedResources: [],
      failedResources: []
    });
  }

  /**
   * 更新进度信息
   * @param name 组名
   * @param resourceId 资源ID
   * @param success 是否成功加载
   */
  private updateProgress(name: string, resourceId: string, success: boolean): void {
    // 检查组名是否存在
    if (!this.progress.has(name)) {
      return;
    }

    // 获取进度信息
    const progressInfo = this.progress.get(name)!;

    // 更新当前加载资源
    progressInfo.currentResource = resourceId;

    // 如果成功加载，则更新已加载资源
    if (success) {
      if (!progressInfo.loadedResources.includes(resourceId)) {
        progressInfo.loadedResources.push(resourceId);
        progressInfo.loaded++;
      }
    } else {
      // 如果加载失败，则更新失败资源
      if (!progressInfo.failedResources.includes(resourceId)) {
        progressInfo.failedResources.push(resourceId);
      }
    }

    // 更新进度
    progressInfo.progress = progressInfo.loaded / progressInfo.total;

    // 更新时间信息
    if (this.enableProgressEstimation) {
      const now = Date.now();
      const elapsedTime = now - this.loadStartTime;
      progressInfo.elapsedTime = elapsedTime;

      // 计算加载速度（每秒加载资源数）
      if (now - this.lastProgressUpdateTime > 1000) {
        const timeDiff = (now - this.lastProgressUpdateTime) / 1000;
        const loadedDiff = progressInfo.loaded - this.lastLoadedCount;
        progressInfo.loadingSpeed = loadedDiff / timeDiff;

        // 更新上次进度更新时间和加载资源数
        this.lastProgressUpdateTime = now;
        this.lastLoadedCount = progressInfo.loaded;

        // 估计剩余时间
        if (progressInfo.loadingSpeed > 0) {
          const remainingResources = progressInfo.total - progressInfo.loaded;
          progressInfo.estimatedTimeRemaining = (remainingResources / progressInfo.loadingSpeed) * 1000;
        }
      }
    }

    // 发出进度更新事件
    this.emit('progressUpdated', progressInfo);
  }

  /**
   * 加载预加载组
   * @param name 组名
   * @param onProgress 进度回调
   * @returns Promise，解析为加载结果
   */
  public async loadGroup(
    name: string,
    onProgress?: (progress: PreloadProgressInfo) => void
  ): Promise<PreloadProgressInfo> {
    // 检查组名是否存在
    if (!this.groups.has(name)) {
      throw new Error(`找不到预加载组: ${name}`);
    }

    // 如果正在加载，则返回
    if (this.loading && !this.paused) {
      throw new Error('已有预加载组正在加载');
    }

    // 获取组信息
    const group = this.groups.get(name)!;

    // 重置进度信息
    this.resetProgress(name);

    // 设置当前加载组
    this.currentGroup = name;
    this.loading = true;
    this.paused = false;

    // 初始化时间信息
    this.loadStartTime = Date.now();
    this.lastProgressUpdateTime = this.loadStartTime;
    this.lastLoadedCount = 0;

    // 发出加载开始事件
    this.emit('loadStart', { group: name });

    if (this.debug) {
      console.log(`[ResourcePreloader] 开始加载组: ${name}`);
    }

    try {
      // 如果启用自动加载依赖组，则先加载依赖组
      if (this.autoLoadDependencies && group.dependencies && group.dependencies.length > 0) {
        for (const depName of group.dependencies) {
          if (this.debug) {
            console.log(`[ResourcePreloader] 加载依赖组: ${depName}`);
          }
          await this.loadGroup(depName);
        }
      }

      // 准备加载队列
      this.prepareLoadQueue(name);

      // 开始加载
      await this.startLoading(onProgress);

      // 获取最终进度信息
      const progressInfo = this.progress.get(name)!;

      // 发出加载完成事件
      this.emit('loadComplete', progressInfo);

      if (this.debug) {
        console.log(`[ResourcePreloader] 完成加载组: ${name}, 加载: ${progressInfo.loaded}/${progressInfo.total}`);
      }

      return progressInfo;
    } catch (error) {
      // 发出加载错误事件
      this.emit('loadError', { group: name, error });

      if (this.debug) {
        console.error(`[ResourcePreloader] 加载组失败: ${name}`, error);
      }

      throw error;
    } finally {
      // 重置状态
      this.currentGroup = null;
      this.loading = false;
      this.paused = false;
      this.loadQueue = [];
      this.currentConcurrentLoads = 0;
    }
  }

  /**
   * 暂停加载
   * @returns 是否成功暂停
   */
  public pause(): boolean {
    // 如果未在加载或已暂停，则返回
    if (!this.loading || this.paused) {
      return false;
    }

    this.paused = true;
    this.emit('loadPaused', { group: this.currentGroup });

    if (this.debug) {
      console.log(`[ResourcePreloader] 暂停加载组: ${this.currentGroup}`);
    }

    return true;
  }

  /**
   * 恢复加载
   * @returns 是否成功恢复
   */
  public resume(): boolean {
    // 如果未在加载或未暂停，则返回
    if (!this.loading || !this.paused) {
      return false;
    }

    this.paused = false;
    this.emit('loadResumed', { group: this.currentGroup });

    if (this.debug) {
      console.log(`[ResourcePreloader] 恢复加载组: ${this.currentGroup}`);
    }

    // 继续加载
    this.startLoading();

    return true;
  }

  /**
   * 取消加载
   * @returns 是否成功取消
   */
  public cancel(): boolean {
    // 如果未在加载，则返回
    if (!this.loading) {
      return false;
    }

    // 重置状态
    this.currentGroup = null;
    this.loading = false;
    this.paused = false;
    this.loadQueue = [];
    this.currentConcurrentLoads = 0;

    this.emit('loadCancelled');

    if (this.debug) {
      console.log(`[ResourcePreloader] 取消加载`);
    }

    return true;
  }

  /**
   * 准备加载队列
   * @param name 组名
   */
  private prepareLoadQueue(name: string): void {
    // 检查组名是否存在
    if (!this.groups.has(name)) {
      return;
    }

    // 获取组信息
    const group = this.groups.get(name)!;

    // 清空加载队列
    this.loadQueue = [];

    // 添加资源到加载队列
    for (const resource of group.resources) {
      // 检查资源是否已加载
      if (this.resourceManager.getResourceData(resource.id)) {
        // 更新进度信息
        this.updateProgress(name, resource.id, true);
        continue;
      }

      // 添加到加载队列
      this.loadQueue.push(resource);
    }

    // 按优先级排序
    this.loadQueue.sort((a, b) => (b.priority || 0) - (a.priority || 0));

    if (this.debug) {
      console.log(`[ResourcePreloader] 准备加载队列: ${name}, 队列长度: ${this.loadQueue.length}`);
    }
  }

  /**
   * 开始加载
   * @param onProgress 进度回调
   * @returns Promise
   */
  private startLoading(onProgress?: (progress: PreloadProgressInfo) => void): Promise<void> {
    return new Promise((resolve, reject) => {
      // 如果队列为空，则直接完成
      if (this.loadQueue.length === 0) {
        resolve();
        return;
      }

      // 如果已暂停，则等待恢复
      if (this.paused) {
        return;
      }

      // 设置进度回调
      if (onProgress && this.currentGroup) {
        const progressHandler = (progress: PreloadProgressInfo) => {
          if (progress.group === this.currentGroup) {
            onProgress(progress);
          }
        };

        this.on('progressUpdated', progressHandler);

        // 加载完成后移除监听器
        this.once('loadComplete', () => {
          this.off('progressUpdated', progressHandler);
        });

        // 加载错误后移除监听器
        this.once('loadError', () => {
          this.off('progressUpdated', progressHandler);
        });
      }

      // 加载下一个资源
      const loadNext = () => {
        // 如果队列为空，则完成
        if (this.loadQueue.length === 0) {
          resolve();
          return;
        }

        // 如果已暂停，则等待恢复
        if (this.paused) {
          return;
        }

        // 如果当前并发加载数达到最大值，则等待
        if (this.currentConcurrentLoads >= this.maxConcurrentLoads) {
          return;
        }

        // 增加并发加载计数
        this.currentConcurrentLoads++;

        // 获取下一个资源
        const resource = this.loadQueue.shift()!;

        if (this.debug) {
          console.log(`[ResourcePreloader] 加载资源: ${resource.id}`);
        }

        // 更新当前加载资源
        if (this.currentGroup) {
          const progressInfo = this.progress.get(this.currentGroup)!;
          progressInfo.currentResource = resource.id;
          this.emit('progressUpdated', progressInfo);
        }

        // 加载资源
        this.loadResource(resource)
          .then(() => {
            // 减少并发加载计数
            this.currentConcurrentLoads--;

            // 更新进度信息
            if (this.currentGroup) {
              this.updateProgress(this.currentGroup, resource.id, true);
            }

            // 继续加载下一个资源
            loadNext();
          })
          .catch(error => {
            // 减少并发加载计数
            this.currentConcurrentLoads--;

            // 更新进度信息
            if (this.currentGroup) {
              this.updateProgress(this.currentGroup, resource.id, false);
            }

            if (this.debug) {
              console.error(`[ResourcePreloader] 加载资源失败: ${resource.id}`, error);
            }

            // 继续加载下一个资源
            loadNext();
          });

        // 继续加载下一个资源
        loadNext();
      };

      // 开始加载
      for (let i = 0; i < this.maxConcurrentLoads; i++) {
        loadNext();
      }
    });
  }

  /**
   * 加载资源
   * @param resource 资源信息
   * @param retryCount 当前重试次数
   * @returns Promise
   */
  private async loadResource(
    resource: PreloadResourceInfo,
    retryCount: number = 0
  ): Promise<void> {
    try {
      // 加载资源
      await this.resourceManager.load(resource.id, resource.type, resource.url, resource.priority);
    } catch (error) {
      // 如果重试次数未达到最大值，则重试
      if (retryCount < this.retryCount) {
        if (this.debug) {
          console.log(`[ResourcePreloader] 重试加载资源: ${resource.id}, 重试次数: ${retryCount + 1}/${this.retryCount}`);
        }

        // 等待重试延迟
        await new Promise(resolve => setTimeout(resolve, this.retryDelay));

        // 重试加载
        return this.loadResource(resource, retryCount + 1);
      }

      // 否则抛出错误
      throw error;
    }
  }

  /**
   * 获取组信息
   * @param name 组名
   * @returns 组信息
   */
  public getGroup(name: string): PreloadGroupInfo | null {
    return this.groups.get(name) || null;
  }

  /**
   * 获取所有组
   * @returns 组信息数组
   */
  public getAllGroups(): PreloadGroupInfo[] {
    return Array.from(this.groups.values());
  }

  /**
   * 获取进度信息
   * @param name 组名
   * @returns 进度信息
   */
  public getProgress(name: string): PreloadProgressInfo | null {
    return this.progress.get(name) || null;
  }

  /**
   * 清除所有组
   */
  public clearGroups(): void {
    // 如果正在加载，则无法清除
    if (this.loading) {
      if (this.debug) {
        console.warn(`[ResourcePreloader] 无法清除正在加载的组`);
      }
      return;
    }

    this.groups.clear();
    this.progress.clear();
    this.emit('groupsCleared');

    if (this.debug) {
      console.log(`[ResourcePreloader] 清除所有组`);
    }
  }

  /**
   * 销毁预加载器
   */
  public dispose(): void {
    // 取消加载
    this.cancel();

    // 清除所有组
    this.clearGroups();

    // 移除所有事件监听器
    this.removeAllListeners();

    this.initialized = false;

    if (this.debug) {
      console.log(`[ResourcePreloader] 销毁预加载器`);
    }
  }
}