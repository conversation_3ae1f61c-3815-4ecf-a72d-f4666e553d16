/**
 * 资源管理模块
 * 导出所有资源管理相关的类和接口
 */

// 导出原有资源管理系统
// 导出资产管理器
export { AssetManager } from './AssetManager';
export type { AssetInfo, AssetManagerOptions } from './AssetManager';
export { AssetType } from './ResourceManager';

// 导出资产加载器
export { AssetLoader } from './AssetLoader';

// 导出资源管理器
export { ResourceManager } from './ResourceManager';
export type {
  ResourceState,
  ResourceInfo,
  ResourceManagerOptions
} from './ResourceManager';

// 导出资源依赖管理器
export { ResourceDependencyManager } from './ResourceDependencyManager';
export type {
  DependencyType,
  DependencyInfo
} from './ResourceDependencyManager';

// 导出资源预加载器
export { ResourcePreloader } from './ResourcePreloader';
export type {
  PreloadResourceInfo,
  PreloadGroupInfo,
  PreloadProgressInfo,
  ResourcePreloaderOptions
} from './ResourcePreloader';

// 导出增强版资源管理系统
// 导出增强资源加载器
export { EnhancedAssetLoader, LoaderOptions } from './EnhancedAssetLoader';

// 导出增强资源管理器
export {
  EnhancedResourceManager,
  EnhancedResourceManagerOptions,
  ResourceInfo as EnhancedResourceInfo
} from './EnhancedResourceManager';

// 导出增强资源依赖管理器
export {
  DependencyType as EnhancedDependencyType,
  EnhancedResourceDependencyManager,
  EnhancedResourceDependencyManagerOptions,
  DependencyInfo as EnhancedDependencyInfo
} from './EnhancedResourceDependencyManager';

// 导出增强资源预加载器
export {
  EnhancedResourcePreloader,
  EnhancedResourcePreloaderOptions,
  PreloadResourceInfo as EnhancedPreloadResourceInfo,
  PreloadGroupInfo as EnhancedPreloadGroupInfo,
  PreloadProgressInfo as EnhancedPreloadProgressInfo
} from './EnhancedResourcePreloader';

// 导出增强资源管理系统
export {
  EnhancedResourceSystem,
  EnhancedResourceSystemOptions
} from './EnhancedResourceSystem';

// 导出示例
export { ResourceSystemExample } from './examples/ResourceSystemExample';

/**
 * 创建默认资源管理系统实例
 * @returns 资源管理系统实例
 */
export function createResourceSystem(): EnhancedResourceSystem {
  const resourceSystem = new EnhancedResourceSystem();
  resourceSystem.initialize();
  return resourceSystem;
}
