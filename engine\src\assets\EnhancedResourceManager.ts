/**
 * 增强资源管理器
 * 提供更高效的资源加载、缓存和管理功能
 */
import * as THREE from 'three';
import { EventEmitter } from '../utils/EventEmitter';
import { AssetType, ResourceState } from './ResourceManager';
import { EnhancedAssetLoader, LoaderOptions } from './EnhancedAssetLoader';
import { EnhancedResourceVersionManager } from './EnhancedResourceVersionManager';

/**
 * 资源信息
 */
export interface ResourceInfo {
  /** 资源ID */
  id: string;
  /** 资源类型 */
  type: AssetType;
  /** 资源URL */
  url: string;
  /** 资源数据 */
  data: any;
  /** 资源状态 */
  state: ResourceState;
  /** 引用计数 */
  refCount: number;
  /** 上次访问时间 */
  lastAccessTime: number;
  /** 资源大小（字节） */
  size: number;
  /** 加载错误 */
  error?: Error;
  /** 资源元数据 */
  metadata?: Record<string, any>;
  /** 资源优先级 */
  priority?: number;
  /** 资源组 */
  group?: string;
  /** 资源标签 */
  tags?: string[];
}

/**
 * 资源加载请求
 */
interface LoadRequest {
  /** 资源ID */
  id: string;
  /** 资源类型 */
  type: AssetType;
  /** 资源URL */
  url: string;
  /** 解析函数 */
  resolve: (data: any) => void;
  /** 拒绝函数 */
  reject: (error: Error) => void;
  /** 资源优先级 */
  priority?: number;
}

/**
 * 增强资源管理器选项
 */
export interface EnhancedResourceManagerOptions {
  /** 最大缓存大小（字节） */
  maxCacheSize?: number;
  /** 缓存清理阈值（0-1） */
  cleanupThreshold?: number;
  /** 缓存清理间隔（毫秒） */
  cleanupInterval?: number;
  /** 是否启用自动清理 */
  autoCleanup?: boolean;
  /** 是否启用预加载 */
  enablePreload?: boolean;
  /** 最大并发加载数 */
  maxConcurrentLoads?: number;
  /** 加载重试次数 */
  retryCount?: number;
  /** 重试延迟（毫秒） */
  retryDelay?: number;
  /** 加载超时（毫秒） */
  loadTimeout?: number;
  /** 是否启用资源压缩 */
  enableCompression?: boolean;
  /** 是否启用资源版本控制 */
  enableVersioning?: boolean;
  /** 资源版本查询参数名 */
  versionQueryParam?: string;
  /** 资源版本 */
  resourceVersion?: string;
  /** 加载器选项 */
  loaderOptions?: LoaderOptions;
  /** 是否启用调试模式 */
  debug?: boolean;
}

/**
 * 增强资源管理器
 */
export class EnhancedResourceManager extends EventEmitter {
  /** 资源映射 */
  private resources: Map<string, ResourceInfo> = new Map();

  /** 加载中的资源 */
  private loadingResources: Map<string, Promise<any>> = new Map();

  /** 最大缓存大小（字节） */
  private maxCacheSize: number;

  /** 当前缓存大小（字节） */
  private currentCacheSize: number = 0;

  /** 缓存清理阈值（0-1） */
  private cleanupThreshold: number;

  /** 缓存清理间隔（毫秒） */
  private cleanupInterval: number;

  /** 是否启用自动清理 */
  private autoCleanup: boolean;

  /** 是否启用预加载 */
  private enablePreload: boolean;

  /** 最大并发加载数 */
  private maxConcurrentLoads: number;

  /** 当前并发加载数 */
  private currentConcurrentLoads: number = 0;

  /** 加载队列 */
  private loadQueue: LoadRequest[] = [];

  /** 加载重试次数 */
  private retryCount: number;

  /** 重试延迟（毫秒） */
  private retryDelay: number;

  /** 加载超时（毫秒） */
  private loadTimeout: number;

  /** 是否启用资源压缩 */
  private enableCompression: boolean;

  /** 是否启用资源版本控制 */
  private enableVersioning: boolean;

  /** 资源版本查询参数名 */
  private versionQueryParam: string;

  /** 资源版本 */
  private resourceVersion: string;

  /** 资产加载器 */
  private loader: EnhancedAssetLoader;

  /** 清理定时器ID */
  private cleanupTimerId: number | null = null;

  /** 是否已初始化 */
  private initialized: boolean = false;

  /** 是否启用调试模式 */
  private debug: boolean;

  /** 资源版本管理器 */
  private versionManager: EnhancedResourceVersionManager;

  /**
   * 创建增强资源管理器实例
   * @param options 资源管理器选项
   */
  constructor(options: EnhancedResourceManagerOptions = {}) {
    super();

    // 设置选项
    this.maxCacheSize = options.maxCacheSize || 1024 * 1024 * 200; // 默认200MB
    this.cleanupThreshold = options.cleanupThreshold || 0.8; // 默认80%
    this.cleanupInterval = options.cleanupInterval || 60000; // 默认1分钟
    this.autoCleanup = options.autoCleanup !== undefined ? options.autoCleanup : true;
    this.enablePreload = options.enablePreload !== undefined ? options.enablePreload : true;
    this.maxConcurrentLoads = options.maxConcurrentLoads || 8;
    this.retryCount = options.retryCount || 3;
    this.retryDelay = options.retryDelay || 1000;
    this.loadTimeout = options.loadTimeout || 30000;
    this.enableCompression = options.enableCompression !== undefined ? options.enableCompression : true;
    this.enableVersioning = options.enableVersioning !== undefined ? options.enableVersioning : false;
    this.versionQueryParam = options.versionQueryParam || 'v';
    this.resourceVersion = options.resourceVersion || '';
    this.debug = options.debug !== undefined ? options.debug : false;

    // 创建资产加载器
    this.loader = new EnhancedAssetLoader(options.loaderOptions);

    // 创建资源版本管理器
    this.versionManager = EnhancedResourceVersionManager.getInstance({
      enabled: this.enableVersioning,
      debug: this.debug
    });

    // 设置加载器事件
    this.loader.on('loadStart', (data) => this.emit('loaderStart', data));
    this.loader.on('loadComplete', () => this.emit('loaderComplete'));
    this.loader.on('loadProgress', (data) => this.emit('loaderProgress', data));
    this.loader.on('loadError', (data) => this.emit('loaderError', data));

    // 转发版本管理器事件
    this.versionManager.on('versionCreated', (data) => this.emit('versionCreated', data));
    this.versionManager.on('versionDeleted', (data) => this.emit('versionDeleted', data));
    this.versionManager.on('versionRollback', (data) => this.emit('versionRollback', data));
    this.versionManager.on('versionCompared', (data) => this.emit('versionCompared', data));
    this.versionManager.on('versionTagged', (data) => this.emit('versionTagged', data));
  }

  /**
   * 初始化资源管理器
   */
  public initialize(): void {
    if (this.initialized) {
      return;
    }

    // 如果启用自动清理，则启动清理定时器
    if (this.autoCleanup) {
      this.startCleanupTimer();
    }

    this.initialized = true;
    this.emit('initialized');
  }

  /**
   * 加载资源
   * @param id 资源ID
   * @param type 资源类型
   * @param url 资源URL
   * @param priority 资源优先级（0-100，数值越大优先级越高）
   * @returns Promise，解析为资源数据
   */
  public async load(id: string, type: AssetType, url: string, priority: number = 0): Promise<any> {
    // 检查资源是否已存在
    if (this.resources.has(id)) {
      const resource = this.resources.get(id)!;

      // 如果已加载，则更新访问时间并返回数据
      if (resource.state === ResourceState.LOADED) {
        resource.lastAccessTime = Date.now();
        resource.refCount++;

        if (this.debug) {
          console.log(`[ResourceManager] 使用缓存资源: ${id}`);
        }

        return resource.data;
      }

      // 如果加载失败，则重新加载
      if (resource.state === ResourceState.ERROR) {
        resource.state = ResourceState.UNLOADED;
      }

      // 如果未加载，则继续加载流程
    }

    // 检查是否已在加载中
    if (this.loadingResources.has(id)) {
      if (this.debug) {
        console.log(`[ResourceManager] 资源正在加载中: ${id}`);
      }

      return this.loadingResources.get(id);
    }

    // 创建加载Promise
    const loadPromise = new Promise<any>((resolve, reject) => {
      // 如果当前并发加载数达到最大值，则加入队列
      if (this.currentConcurrentLoads >= this.maxConcurrentLoads) {
        if (this.debug) {
          console.log(`[ResourceManager] 加入加载队列: ${id}`);
        }

        this.loadQueue.push({ id, type, url, resolve, reject, priority });

        // 按优先级排序队列
        this.loadQueue.sort((a, b) => (b.priority || 0) - (a.priority || 0));

        return;
      }

      // 增加并发加载计数
      this.currentConcurrentLoads++;

      // 更新资源状态
      this.updateResourceState(id, type, url, ResourceState.LOADING);

      // 发出加载开始事件
      this.emit('loadStart', { id, type, url });

      // 执行实际加载
      this.loadResource(id, type, url)
        .then(data => {
          // 更新资源状态
          this.updateResourceState(id, type, url, ResourceState.LOADED, data);

          // 发出加载完成事件
          this.emit('loadComplete', { id, type, url, data });

          // 减少并发加载计数
          this.currentConcurrentLoads--;

          // 从加载中资源映射中移除
          this.loadingResources.delete(id);

          // 处理队列中的下一个加载请求
          this.processNextQueuedLoad();

          resolve(data);
        })
        .catch(error => {
          // 更新资源状态
          this.updateResourceState(id, type, url, ResourceState.ERROR, undefined, error);

          // 发出加载错误事件
          this.emit('loadError', { id, type, url, error });

          // 减少并发加载计数
          this.currentConcurrentLoads--;

          // 从加载中资源映射中移除
          this.loadingResources.delete(id);

          // 处理队列中的下一个加载请求
          this.processNextQueuedLoad();

          reject(error);
        });
    });

    // 添加到加载中资源映射
    this.loadingResources.set(id, loadPromise);

    return loadPromise;
  }

  /**
   * 实际加载资源
   * @param id 资源ID
   * @param type 资源类型
   * @param url 资源URL
   * @param retryCount 当前重试次数
   * @returns Promise，解析为资源数据
   */
  private async loadResource(id: string, type: AssetType, url: string, retryCount: number = 0): Promise<any> {
    try {
      // 处理URL版本控制
      const finalUrl = this.processUrl(url);

      if (this.debug) {
        console.log(`[ResourceManager] 加载资源: ${id}, URL: ${finalUrl}`);
      }

      // 创建加载超时Promise
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error(`加载资源超时: ${id}`)), this.loadTimeout);
      });

      // 创建加载Promise
      const loadPromise = this.loader.load(type, finalUrl);

      // 使用Promise.race竞争加载和超时
      const data = await Promise.race([loadPromise, timeoutPromise]);

      return data;
    } catch (error) {
      if (this.debug) {
        console.error(`[ResourceManager] 加载资源失败: ${id}, 错误: ${(error as Error).message}`);
      }

      // 如果重试次数未达到最大值，则重试
      if (retryCount < this.retryCount) {
        if (this.debug) {
          console.log(`[ResourceManager] 重试加载资源: ${id}, 重试次数: ${retryCount + 1}/${this.retryCount}`);
        }

        // 等待重试延迟
        await new Promise(resolve => setTimeout(resolve, this.retryDelay));

        // 重试加载
        return this.loadResource(id, type, url, retryCount + 1);
      }

      throw new Error(`加载资源失败: ${(error as Error).message}`);
    }
  }

  /**
   * 处理URL（添加版本号等）
   * @param url 原始URL
   * @returns 处理后的URL
   */
  private processUrl(url: string): string {
    // 如果未启用版本控制或版本为空，则直接返回URL
    if (!this.enableVersioning || !this.resourceVersion) {
      return url;
    }

    // 如果URL已包含查询参数，则添加版本号
    if (url.includes('?')) {
      return `${url}&${this.versionQueryParam}=${this.resourceVersion}`;
    }

    // 否则，添加版本号作为第一个查询参数
    return `${url}?${this.versionQueryParam}=${this.resourceVersion}`;
  }

  /**
   * 处理队列中的下一个加载请求
   */
  private processNextQueuedLoad(): void {
    // 如果队列为空，则返回
    if (this.loadQueue.length === 0) {
      return;
    }

    // 获取下一个请求
    const request = this.loadQueue.shift()!;

    // 加载资源
    this.load(request.id, request.type, request.url, request.priority)
      .then(data => request.resolve(data))
      .catch(error => request.reject(error));
  }

  /**
   * 更新资源状态
   * @param id 资源ID
   * @param type 资源类型
   * @param url 资源URL
   * @param state 资源状态
   * @param data 资源数据
   * @param error 加载错误
   */
  private updateResourceState(
    id: string,
    type: AssetType,
    url: string,
    state: ResourceState,
    data?: any,
    error?: Error
  ): void {
    // 获取资源，如果不存在则创建
    let resource = this.resources.get(id);

    if (!resource) {
      resource = {
        id,
        type,
        url,
        data: undefined,
        state: ResourceState.UNLOADED,
        refCount: 0,
        lastAccessTime: Date.now(),
        size: 0,
      };

      this.resources.set(id, resource);
    }

    // 更新资源状态
    resource.state = state;

    // 如果提供了数据，则更新数据和大小
    if (data !== undefined) {
      // 如果之前有数据，则减少缓存大小
      if (resource.data) {
        this.currentCacheSize -= resource.size;
      }

      resource.data = data;
      resource.refCount = 1;
      resource.lastAccessTime = Date.now();

      // 计算资源大小
      resource.size = this.calculateResourceSize(data);

      // 增加缓存大小
      this.currentCacheSize += resource.size;

      // 如果超过最大缓存大小，则触发清理
      if (this.currentCacheSize > this.maxCacheSize * this.cleanupThreshold) {
        this.cleanup();
      }
    }

    // 如果提供了错误，则更新错误
    if (error !== undefined) {
      resource.error = error;
    }
  }

  /**
   * 计算资源大小（字节）
   * @param data 资源数据
   * @returns 资源大小
   */
  private calculateResourceSize(data: any): number {
    if (!data) {
      return 0;
    }

    // 根据数据类型计算大小
    if (data instanceof ArrayBuffer) {
      return data.byteLength;
    } else if (data instanceof Blob) {
      return data.size;
    } else if (data instanceof THREE.Texture) {
      // 估算纹理大小
      const width = data.image?.width || 0;
      const height = data.image?.height || 0;
      // 假设每像素4字节（RGBA）
      return width * height * 4;
    } else if (data instanceof THREE.Mesh) {
      // 估算网格大小
      let size = 0;

      // 计算几何体大小
      if (data.geometry) {
        const geometry = data.geometry;

        // 计算顶点数据大小
        if (geometry.attributes.position) {
          size += geometry.attributes.position.array.byteLength;
        }

        // 计算法线数据大小
        if (geometry.attributes.normal) {
          size += geometry.attributes.normal.array.byteLength;
        }

        // 计算UV数据大小
        if (geometry.attributes.uv) {
          size += geometry.attributes.uv.array.byteLength;
        }

        // 计算索引数据大小
        if (geometry.index) {
          size += geometry.index.array.byteLength;
        }
      }

      return size;
    } else if (typeof data === 'string') {
      // 字符串大小（UTF-16编码，每个字符2字节）
      return data.length * 2;
    } else if (typeof data === 'object') {
      // 对象大小估算
      try {
        const json = JSON.stringify(data);
        return json.length * 2;
      } catch (e) {
        // 如果无法序列化，则使用固定大小
        return 1024;
      }
    }

    // 默认大小
    return 1024;
  }

  /**
   * 释放资源
   * @param id 资源ID
   * @returns 是否成功释放
   */
  public release(id: string): boolean {
    // 检查资源是否存在
    if (!this.resources.has(id)) {
      return false;
    }

    const resource = this.resources.get(id)!;

    // 减少引用计数
    resource.refCount--;

    // 如果引用计数为0，则考虑释放资源
    if (resource.refCount <= 0) {
      if (this.debug) {
        console.log(`[ResourceManager] 释放资源: ${id}`);
      }

      // 如果资源已加载，则减少缓存大小
      if (resource.state === ResourceState.LOADED && resource.data) {
        this.currentCacheSize -= resource.size;

        // 尝试释放资源内存
        this.disposeResourceData(resource.data);
      }

      // 清除资源数据
      resource.data = undefined;
      resource.state = ResourceState.UNLOADED;
      resource.refCount = 0;

      // 发出资源释放事件
      this.emit('resourceReleased', { id });

      return true;
    }

    return false;
  }

  /**
   * 释放资源数据内存
   * @param data 资源数据
   */
  private disposeResourceData(data: any): void {
    if (!data) {
      return;
    }

    try {
      // 根据数据类型释放内存
      if (typeof data.dispose === 'function') {
        // 如果对象有dispose方法，则调用它
        (data as any).dispose();
      } else if (data instanceof THREE.Texture) {
        (data as any).dispose();
      } else if (data instanceof THREE.Material) {
        (data as any).dispose();
      } else if (data instanceof THREE.Mesh) {
        // 释放几何体
        if (data.geometry) {
          (data.geometry as any).dispose();
        }

        // 释放材质
        if (data.material) {
          if (Array.isArray(data.material)) {
            data.material.forEach(mat => (mat as any).dispose());
          } else {
            (data.material as any).dispose();
          }
        }
      }
    } catch (error) {
      console.error(`释放资源数据失败:`, error);
    }
  }

  /**
   * 获取资源
   * @param id 资源ID
   * @returns 资源信息，如果不存在则返回null
   */
  public getResource(id: string): ResourceInfo | null {
    const resource = this.resources.get(id);

    if (!resource) {
      return null;
    }

    // 更新访问时间
    resource.lastAccessTime = Date.now();

    return resource;
  }

  /**
   * 获取资源数据
   * @param id 资源ID
   * @returns 资源数据，如果不存在或未加载则返回null
   */
  public getResourceData(id: string): any {
    const resource = this.getResource(id);

    if (!resource || resource.state !== ResourceState.LOADED) {
      return null;
    }

    return resource.data;
  }

  /**
   * 预加载资源
   * @param id 资源ID
   * @param type 资源类型
   * @param url 资源URL
   * @param priority 资源优先级
   * @returns Promise，解析为资源数据
   */
  public preload(id: string, type: AssetType, url: string, priority: number = 0): Promise<any> {
    // 如果未启用预加载，则直接返回
    if (!this.enablePreload) {
      return Promise.resolve(null);
    }

    if (this.debug) {
      console.log(`[ResourceManager] 预加载资源: ${id}`);
    }

    // 调用加载方法
    return this.load(id, type, url, priority);
  }

  /**
   * 批量预加载资源
   * @param resources 资源数组
   * @param onProgress 进度回调
   * @returns Promise，解析为资源数据映射
   */
  public async preloadBatch(
    resources: Array<{ id: string; type: AssetType; url: string; priority?: number }>,
    onProgress?: (loaded: number, total: number) => void
  ): Promise<Map<string, any>> {
    // 如果未启用预加载，则直接返回
    if (!this.enablePreload) {
      return Promise.resolve(new Map());
    }

    const total = resources.length;
    let loaded = 0;
    const results = new Map<string, any>();

    if (this.debug) {
      console.log(`[ResourceManager] 批量预加载 ${total} 个资源`);
    }

    // 按优先级排序
    const sortedResources = [...resources].sort((a, b) => (b.priority || 0) - (a.priority || 0));

    // 创建加载Promise数组
    const promises = sortedResources.map(async ({ id, type, url, priority }) => {
      try {
        const data = await this.load(id, type, url, priority);
        results.set(id, data);
      } catch (error) {
        console.error(`预加载资源 ${id} 失败:`, error);
      } finally {
        loaded++;

        if (onProgress) {
          onProgress(loaded, total);
        }
      }
    });

    // 等待所有加载完成
    await Promise.all(promises);

    return results;
  }

  /**
   * 清理缓存
   */
  public cleanup(): void {
    // 如果缓存大小未超过阈值，则不需要清理
    if (this.currentCacheSize <= this.maxCacheSize * this.cleanupThreshold) {
      return;
    }

    if (this.debug) {
      console.log(`[ResourceManager] 清理缓存, 当前大小: ${this.formatSize(this.currentCacheSize)}`);
    }

    // 获取所有资源
    const resources = Array.from(this.resources.values());

    // 按照最后访问时间排序（最早访问的在前面）
    resources.sort((a, b) => a.lastAccessTime - b.lastAccessTime);

    // 计算需要释放的空间
    const targetSize = this.maxCacheSize * 0.7; // 释放到70%
    const sizeToFree = this.currentCacheSize - targetSize;
    let freedSize = 0;

    // 释放资源直到达到目标大小
    for (const resource of resources) {
      // 如果资源未加载或引用计数大于0，则跳过
      if (resource.state !== ResourceState.LOADED || resource.refCount > 0) {
        continue;
      }

      // 释放资源
      const size = resource.size;
      this.release(resource.id);

      // 增加已释放大小
      freedSize += size;

      // 如果已释放足够空间，则停止
      if (freedSize >= sizeToFree) {
        break;
      }
    }

    if (this.debug) {
      console.log(`[ResourceManager] 缓存清理完成, 释放: ${this.formatSize(freedSize)}, 当前大小: ${this.formatSize(this.currentCacheSize)}`);
    }

    // 发出缓存清理事件
    this.emit('cacheCleanup', { freedSize });
  }

  /**
   * 格式化大小
   * @param bytes 字节数
   * @returns 格式化后的大小字符串
   */
  private formatSize(bytes: number): string {
    if (bytes < 1024) {
      return bytes + ' B';
    } else if (bytes < 1024 * 1024) {
      return (bytes / 1024).toFixed(2) + ' KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
    } else {
      return (bytes / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
    }
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    // 如果已有定时器，则先停止
    if (this.cleanupTimerId !== null) {
      this.stopCleanupTimer();
    }

    // 启动新定时器
    this.cleanupTimerId = window.setInterval(() => {
      this.cleanup();
    }, this.cleanupInterval);
  }

  /**
   * 停止清理定时器
   */
  private stopCleanupTimer(): void {
    if (this.cleanupTimerId !== null) {
      window.clearInterval(this.cleanupTimerId);
      this.cleanupTimerId = null;
    }
  }

  /**
   * 清空缓存
   */
  public clearCache(): void {
    if (this.debug) {
      console.log(`[ResourceManager] 清空缓存`);
    }

    // 释放所有资源
    for (const id of this.resources.keys()) {
      this.release(id);
    }

    // 清空资源映射
    this.resources.clear();

    // 重置缓存大小
    this.currentCacheSize = 0;

    // 发出缓存清空事件
    this.emit('cacheCleared');
  }

  /**
   * 获取缓存统计信息
   * @returns 缓存统计信息
   */
  public getCacheStats(): {
    totalResources: number;
    loadedResources: number;
    errorResources: number;
    totalSize: number;
    maxSize: number;
    usagePercentage: number;
  } {
    // 计算各种资源数量
    let loadedResources = 0;
    let errorResources = 0;

    for (const resource of this.resources.values()) {
      if (resource.state === ResourceState.LOADED) {
        loadedResources++;
      } else if (resource.state === ResourceState.ERROR) {
        errorResources++;
      }
    }

    return {
      totalResources: this.resources.size,
      loadedResources,
      errorResources,
      totalSize: this.currentCacheSize,
      maxSize: this.maxCacheSize,
      usagePercentage: (this.currentCacheSize / this.maxCacheSize) * 100
    };
  }

  /**
   * 获取资源版本管理器
   * @returns 资源版本管理器
   */
  public getVersionManager(): EnhancedResourceVersionManager {
    return this.versionManager;
  }

  /**
   * 创建资源版本
   * @param resourceId 资源ID
   * @param description 版本描述
   * @param tags 版本标签
   * @returns 创建的版本ID
   */
  public createResourceVersion(resourceId: string, description: string = '创建新版本', tags: string[] = []): string | null {
    if (!this.enableVersioning) {
      if (this.debug) {
        console.warn(`[ResourceManager] 版本控制未启用，无法创建版本`);
      }
      return null;
    }

    // 获取资源信息
    const resource = this.getResource(resourceId);
    if (!resource || resource.state !== ResourceState.LOADED) {
      if (this.debug) {
        console.warn(`[ResourceManager] 资源未加载，无法创建版本: ${resourceId}`);
      }
      return null;
    }

    try {
      // 计算资源哈希（简单实现，实际应用中应使用更可靠的哈希算法）
      const hash = this.calculateResourceHash(resource.data);

      // 创建版本
      const version = this.versionManager.createVersion(
        resourceId,
        resource.url,
        resource.type,
        hash,
        resource.size,
        resource.metadata || {},
        description,
        'system', // 用户ID，实际应用中应从认证系统获取
        '系统', // 用户名，实际应用中应从认证系统获取
        tags
      );

      if (this.debug) {
        console.log(`[ResourceManager] 创建资源版本: ${resourceId}, 版本ID: ${version.id}`);
      }

      return version.id;
    } catch (error) {
      console.error(`创建资源版本失败:`, error);
      return null;
    }
  }

  /**
   * 回滚到指定版本
   * @param resourceId 资源ID
   * @param versionId 版本ID
   * @returns 是否成功回滚
   */
  public rollbackToVersion(resourceId: string, versionId: string): boolean {
    if (!this.enableVersioning) {
      if (this.debug) {
        console.warn(`[ResourceManager] 版本控制未启用，无法回滚版本`);
      }
      return false;
    }

    // 获取版本信息
    const version = this.versionManager.getVersion(resourceId, versionId);
    if (!version) {
      if (this.debug) {
        console.warn(`[ResourceManager] 找不到要回滚的版本: ${versionId}`);
      }
      return false;
    }

    try {
      // 实现回滚逻辑
      // 这里的实现取决于具体的资源类型和应用需求
      // 简单实现：重新加载版本的URL

      // 释放当前资源
      this.release(resourceId);

      // 加载版本的URL
      this.load(resourceId, version.type, version.url);

      // 发出版本回滚事件
      this.emit('versionRollback', { resourceId, versionId });

      if (this.debug) {
        console.log(`[ResourceManager] 回滚资源版本: ${resourceId}, 版本ID: ${versionId}`);
      }

      return true;
    } catch (error) {
      console.error(`回滚资源版本失败:`, error);
      return false;
    }
  }

  /**
   * 计算资源哈希
   * @param data 资源数据
   * @returns 资源哈希
   */
  private calculateResourceHash(data: any): string {
    // 简单实现，实际应用中应使用更可靠的哈希算法
    if (!data) {
      return 'empty';
    }

    try {
      if (typeof data === 'string') {
        // 字符串哈希
        let hash = 0;
        for (let i = 0; i < data.length; i++) {
          const char = data.charCodeAt(i);
          hash = ((hash << 5) - hash) + char;
          hash = hash & hash; // Convert to 32bit integer
        }
        return hash.toString(16);
      } else if (data instanceof ArrayBuffer) {
        // ArrayBuffer哈希
        const view = new Uint8Array(data);
        let hash = 0;
        for (let i = 0; i < view.length; i++) {
          hash = ((hash << 5) - hash) + view[i];
          hash = hash & hash;
        }
        return hash.toString(16);
      } else if (typeof data === 'object') {
        // 对象哈希
        try {
          const json = JSON.stringify(data);
          let hash = 0;
          for (let i = 0; i < json.length; i++) {
            const char = json.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash;
          }
          return hash.toString(16);
        } catch (e) {
          // 如果无法序列化，则使用时间戳
          return Date.now().toString(16);
        }
      } else {
        // 其他类型
        return Date.now().toString(16);
      }
    } catch (error) {
      console.error(`计算资源哈希失败:`, error);
      return Date.now().toString(16);
    }
  }

  /**
   * 销毁资源管理器
   */
  public dispose(): void {
    if (this.debug) {
      console.log(`[ResourceManager] 销毁资源管理器`);
    }

    // 停止清理定时器
    this.stopCleanupTimer();

    // 清空缓存
    this.clearCache();

    // 清空加载队列
    this.loadQueue = [];

    // 清空加载中资源映射
    this.loadingResources.clear();

    // 销毁加载器
    (this.loader as any).dispose();

    // 移除所有事件监听器
    this.removeAllListeners();

    this.initialized = false;
  }
}