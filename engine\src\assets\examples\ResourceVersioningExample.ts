/**
 * 资源版本控制示例
 * 演示如何使用资源版本控制功能
 */
import { EnhancedResourceManager } from '../EnhancedResourceManager';
import { EnhancedResourceVersionManager, ResourceVersion } from '../EnhancedResourceVersionManager';
import { AssetType } from '../AssetTypes';

/**
 * 资源版本控制示例
 */
export class ResourceVersioningExample {
  /** 资源管理器 */
  private resourceManager: EnhancedResourceManager;
  
  /** 版本管理器 */
  private versionManager: EnhancedResourceVersionManager;
  
  /** 示例资源ID */
  private exampleResourceId: string = 'example-texture';
  
  /** 示例资源URL */
  private exampleResourceUrl: string = 'assets/textures/example.jpg';
  
  /** 示例资源类型 */
  private exampleResourceType: AssetType = AssetType.TEXTURE;
  
  /**
   * 创建资源版本控制示例实例
   */
  constructor() {
    // 创建资源管理器
    this.resourceManager = new EnhancedResourceManager({
      debug: true,
      enableVersioning: true,
      resourceVersion: '1.0.0'
    });
    
    // 获取版本管理器
    this.versionManager = this.resourceManager.getVersionManager();
    
    // 初始化资源管理器
    this.resourceManager.initialize();
    
    console.log('资源版本控制示例已初始化');
  }
  
  /**
   * 运行示例
   */
  public async run(): Promise<void> {
    console.log('开始运行资源版本控制示例...');
    
    // 加载资源
    await this.loadResource();
    
    // 创建版本
    await this.createVersions();
    
    // 比较版本
    await this.compareVersions();
    
    // 回滚版本
    await this.rollbackVersion();
    
    console.log('资源版本控制示例运行完成');
  }
  
  /**
   * 加载资源
   */
  private async loadResource(): Promise<void> {
    console.log('加载资源...');
    
    try {
      // 加载资源
      const data = await this.resourceManager.load(
        this.exampleResourceId,
        this.exampleResourceType,
        this.exampleResourceUrl
      );
      
      console.log('资源加载成功:', data);
    } catch (error) {
      console.error('资源加载失败:', error);
    }
  }
  
  /**
   * 创建版本
   */
  private async createVersions(): Promise<void> {
    console.log('创建版本...');
    
    // 创建第一个版本
    const version1Id = this.resourceManager.createResourceVersion(
      this.exampleResourceId,
      '初始版本',
      ['initial', 'v1']
    );
    
    console.log('创建版本1成功:', version1Id);
    
    // 模拟资源修改
    await this.simulateResourceModification();
    
    // 创建第二个版本
    const version2Id = this.resourceManager.createResourceVersion(
      this.exampleResourceId,
      '修改后的版本',
      ['modified', 'v2']
    );
    
    console.log('创建版本2成功:', version2Id);
    
    // 显示所有版本
    this.displayAllVersions();
  }
  
  /**
   * 模拟资源修改
   */
  private async simulateResourceModification(): Promise<void> {
    console.log('模拟资源修改...');
    
    // 在实际应用中，这里应该是真实的资源修改操作
    // 例如，修改纹理、模型等
    
    // 这里只是简单地重新加载资源，模拟修改
    await this.resourceManager.load(
      this.exampleResourceId,
      this.exampleResourceType,
      this.exampleResourceUrl + '?modified=true'
    );
    
    console.log('资源修改完成');
  }
  
  /**
   * 比较版本
   */
  private async compareVersions(): Promise<void> {
    console.log('比较版本...');
    
    // 获取所有版本
    const versions = this.versionManager.getVersions(this.exampleResourceId);
    
    if (versions.length < 2) {
      console.log('版本数量不足，无法比较');
      return;
    }
    
    // 获取前两个版本
    const version1 = versions[0];
    const version2 = versions[1];
    
    // 比较版本
    const comparisonResult = this.versionManager.compareVersions(
      this.exampleResourceId,
      version1.id,
      version2.id
    );
    
    if (comparisonResult) {
      console.log('版本比较结果:', comparisonResult);
      console.log('是否有差异:', comparisonResult.hasDifferences);
      console.log('差异类型:', comparisonResult.differenceType);
      
      if (comparisonResult.metadataDifferences) {
        console.log('元数据差异:', comparisonResult.metadataDifferences);
      }
    } else {
      console.log('版本比较失败');
    }
  }
  
  /**
   * 回滚版本
   */
  private async rollbackVersion(): Promise<void> {
    console.log('回滚版本...');
    
    // 获取所有版本
    const versions = this.versionManager.getVersions(this.exampleResourceId);
    
    if (versions.length === 0) {
      console.log('没有可回滚的版本');
      return;
    }
    
    // 获取第一个版本
    const version = versions[0];
    
    // 回滚到第一个版本
    const success = this.resourceManager.rollbackToVersion(
      this.exampleResourceId,
      version.id
    );
    
    if (success) {
      console.log('回滚成功，当前版本:', version.description);
    } else {
      console.log('回滚失败');
    }
  }
  
  /**
   * 显示所有版本
   */
  private displayAllVersions(): void {
    console.log('所有版本:');
    
    // 获取所有版本
    const versions = this.versionManager.getVersions(this.exampleResourceId);
    
    // 显示版本信息
    versions.forEach((version: ResourceVersion, index: number) => {
      console.log(`版本 ${index + 1}:`);
      console.log(`  ID: ${version.id}`);
      console.log(`  描述: ${version.description}`);
      console.log(`  版本号: ${version.version}`);
      console.log(`  创建时间: ${new Date(version.timestamp).toLocaleString()}`);
      console.log(`  创建者: ${version.userName}`);
      console.log(`  标签: ${version.tags?.join(', ') || '无'}`);
      console.log(`  URL: ${version.url}`);
      console.log(`  哈希: ${version.hash}`);
      console.log(`  大小: ${this.formatSize(version.size)}`);
    });
  }
  
  /**
   * 格式化大小
   * @param bytes 字节数
   * @returns 格式化后的大小字符串
   */
  private formatSize(bytes: number): string {
    if (bytes < 1024) {
      return bytes + ' B';
    } else if (bytes < 1024 * 1024) {
      return (bytes / 1024).toFixed(2) + ' KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
    } else {
      return (bytes / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
    }
  }
}

// 导出单例实例
export const resourceVersioningExample = new ResourceVersioningExample();
