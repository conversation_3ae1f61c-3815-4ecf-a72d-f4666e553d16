/**
 * 口型同步组件
 * 用于控制角色的口型同步
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { EventEmitter } from '../../utils/EventEmitter';
import { VisemeType } from './FacialAnimationComponent';

/**
 * 口型同步组件类型
 */
export const LipSyncComponentType = 'LipSyncComponent';

/**
 * 口型同步组件
 */
export class LipSyncComponent extends Component {
  /** 组件类型 */
  static readonly TYPE = LipSyncComponentType;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 是否启用 */
  private enabled: boolean = true;

  /** 当前口型 */
  private currentViseme: VisemeType = VisemeType.SILENT;

  /** 口型权重 */
  private visemeWeight: number = 0.0;

  /** 口型混合速度 */
  private visemeBlendSpeed: number = 10.0;

  /** 口型混合映射 */
  private visemeBlendMap: Map<VisemeType, number> = new Map();

  /** 口型历史 */
  private visemeHistory: { viseme: VisemeType, time: number }[] = [];

  /** 历史长度 */
  private historyLength: number = 10;

  /** 音频源 */
  private audioSource: HTMLAudioElement | null = null;

  /** 音频上下文 */
  private audioContext: AudioContext | null = null;

  /** 音频分析器 */
  private audioAnalyser: AnalyserNode | null = null;

  /** 音频源节点 */
  private sourceNode: MediaElementAudioSourceNode | null = null;

  /** 是否自动检测音频 */
  private autoDetectAudio: boolean = false;

  /**
   * 构造函数
   * @param entity 实体
   */
  constructor(entity: Entity) {
    super(entity);

    // 初始化口型混合映射
    for (const viseme in VisemeType) {
      this.visemeBlendMap.set(VisemeType[viseme], 0);
    }
  }

  /**
   * 获取组件类型
   */
  public getType(): string {
    return LipSyncComponent.TYPE;
  }

  /**
   * 启用组件
   */
  public enable(): void {
    this.enabled = true;
  }

  /**
   * 禁用组件
   */
  public disable(): void {
    this.enabled = false;
  }

  /**
   * 是否启用
   */
  public isEnabled(): boolean {
    return this.enabled;
  }

  /**
   * 设置口型
   * @param viseme 口型类型
   * @param weight 权重
   * @param blendTime 混合时间（秒）
   */
  public setViseme(viseme: VisemeType, weight: number = 1.0, blendTime?: number): void {
    if (!this.enabled) return;

    this.currentViseme = viseme;
    this.visemeWeight = weight;

    if (blendTime !== undefined) {
      this.visemeBlendSpeed = 1.0 / Math.max(0.001, blendTime);
    }

    // 更新混合映射
    for (const [key, _] of this.visemeBlendMap.entries()) {
      if (key === viseme) {
        this.visemeBlendMap.set(key, weight);
      } else {
        this.visemeBlendMap.set(key, 0);
      }
    }

    // 添加到历史
    this.visemeHistory.push({ viseme, time: Date.now() });
    if (this.visemeHistory.length > this.historyLength) {
      this.visemeHistory.shift();
    }

    this.eventEmitter.emit('visemeChange', { viseme, weight });
  }

  /**
   * 重置口型
   */
  public resetViseme(): void {
    this.setViseme(VisemeType.SILENT);
  }

  /**
   * 获取当前口型
   */
  public getCurrentViseme(): VisemeType {
    return this.currentViseme;
  }

  /**
   * 获取当前口型权重
   */
  public getCurrentVisemeWeight(): number {
    return this.visemeWeight;
  }

  /**
   * 获取口型混合映射
   */
  public getVisemeBlendMap(): Map<VisemeType, number> {
    return this.visemeBlendMap;
  }

  /**
   * 获取口型历史
   */
  public getVisemeHistory(): { viseme: VisemeType, time: number }[] {
    return this.visemeHistory;
  }

  /**
   * 设置音频源
   * @param audio 音频元素
   */
  public setAudioSource(audio: HTMLAudioElement): void {
    this.audioSource = audio;

    // 初始化音频分析
    this.initAudioAnalysis();
  }

  /**
   * 初始化音频分析
   */
  private initAudioAnalysis(): void {
    if (!this.audioSource) return;

    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      this.audioAnalyser = this.audioContext.createAnalyser();
      this.audioAnalyser.fftSize = 1024;
      this.audioAnalyser.smoothingTimeConstant = 0.8;

      this.sourceNode = this.audioContext.createMediaElementSource(this.audioSource);
      this.sourceNode.connect(this.audioAnalyser);
      this.audioAnalyser.connect(this.audioContext.destination);

      this.autoDetectAudio = true;
    } catch (error) {
      console.error('初始化音频分析失败:', error);
    }
  }

  /**
   * 分析音频
   */
  private analyzeAudio(): void {
    if (!this.audioAnalyser || !this.autoDetectAudio) return;

    // 获取频率数据
    const dataArray = new Float32Array(this.audioAnalyser.frequencyBinCount);
    this.audioAnalyser.getFloatFrequencyData(dataArray);

    // 计算RMS
    let sum = 0;
    for (let i = 0; i < dataArray.length; i++) {
      // 将dB转换为线性值
      const linear = Math.pow(10, dataArray[i] / 20);
      sum += linear * linear;
    }
    const rms = Math.sqrt(sum / dataArray.length);

    // 如果音量太小，则设置为静默
    if (rms < 0.01) {
      this.setViseme(VisemeType.SILENT);
      return;
    }

    // 简单的频率分析
    let maxBin = 0;
    let maxVal = -Infinity;
    for (let i = 0; i < dataArray.length; i++) {
      if (dataArray[i] > maxVal) {
        maxVal = dataArray[i];
        maxBin = i;
      }
    }

    // 根据频率确定口型
    const sampleRate = this.audioContext!.sampleRate;
    const binWidth = sampleRate / (2 * dataArray.length);
    const frequency = maxBin * binWidth;

    let viseme = VisemeType.AA;
    if (frequency < 200) {
      viseme = VisemeType.MM;
    } else if (frequency < 500) {
      viseme = VisemeType.OU;
    } else if (frequency < 1000) {
      viseme = VisemeType.AA;
    } else if (frequency < 2000) {
      viseme = VisemeType.EE;
    } else if (frequency < 4000) {
      viseme = VisemeType.SS;
    } else {
      viseme = VisemeType.FF;
    }

    this.setViseme(viseme);
  }

  /**
   * 更新组件
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.enabled) return;

    // 如果启用了自动检测音频，分析音频
    if (this.autoDetectAudio) {
      this.analyzeAudio();
    }

    // 更新逻辑可以在这里添加
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public addEventListener(event: string, callback: Function): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  public removeEventListener(event: string, callback: Function): void {
    this.eventEmitter.off(event, callback);
  }
}
