/**
 * 面部动画预设系统
 * 提供预定义的面部动画模板和预设
 */
import type { Entity } from '../../core/Entity';
import { System } from '../../core/System';
import type { World } from '../../core/World';
import { EventEmitter } from '../../utils/EventEmitter';
import { FacialAnimationComponent, FacialExpressionType, VisemeType } from '../components/FacialAnimationComponent';

/**
 * 面部动画预设系统配置
 */
export interface FacialAnimationPresetSystemConfig {
  /** 是否启用调试 */
  debug?: boolean;
  /** 是否自动加载预设 */
  autoLoadPresets?: boolean;
  /** 预设路径 */
  presetsPath?: string;
  /** 是否使用本地存储 */
  useLocalStorage?: boolean;
}

/**
 * 面部动画关键帧
 */
export interface FacialAnimationKeyframe {
  /** 时间（秒） */
  time: number;
  /** 表情类型 */
  expression?: FacialExpressionType;
  /** 表情权重 */
  expressionWeight?: number;
  /** 口型 */
  viseme?: VisemeType;
  /** 口型权重 */
  visemeWeight?: number;
  /** 混合形状权重 */
  blendShapeWeights?: { [key: string]: number };
  /** 缓动类型 */
  easingType?: string;
  /** 缓动参数 */
  easingParams?: any;
}

/**
 * 面部动画预设
 */
export interface FacialAnimationPreset {
  /** 预设ID */
  id: string;
  /** 预设名称 */
  name: string;
  /** 预设描述 */
  description?: string;
  /** 预设类别 */
  category?: string;
  /** 预设标签 */
  tags?: string[];
  /** 预设作者 */
  author?: string;
  /** 预设版本 */
  version?: string;
  /** 预设创建时间 */
  createdAt?: string;
  /** 预设更新时间 */
  updatedAt?: string;
  /** 预设缩略图 */
  thumbnail?: string;
  /** 预设持续时间（秒） */
  duration: number;
  /** 是否循环 */
  loop?: boolean;
  /** 关键帧 */
  keyframes: FacialAnimationKeyframe[];
  /** 元数据 */
  metadata?: any;
}

/**
 * 面部动画模板
 */
export interface FacialAnimationTemplate {
  /** 模板ID */
  id: string;
  /** 模板名称 */
  name: string;
  /** 模板描述 */
  description?: string;
  /** 模板类别 */
  category?: string;
  /** 模板标签 */
  tags?: string[];
  /** 模板作者 */
  author?: string;
  /** 模板版本 */
  version?: string;
  /** 模板创建时间 */
  createdAt?: string;
  /** 模板更新时间 */
  updatedAt?: string;
  /** 模板缩略图 */
  thumbnail?: string;
  /** 模板参数 */
  parameters: {
    /** 参数ID */
    id: string;
    /** 参数名称 */
    name: string;
    /** 参数描述 */
    description?: string;
    /** 参数类型 */
    type: 'number' | 'boolean' | 'string' | 'enum';
    /** 参数默认值 */
    defaultValue: any;
    /** 参数最小值（数字类型） */
    min?: number;
    /** 参数最大值（数字类型） */
    max?: number;
    /** 参数步长（数字类型） */
    step?: number;
    /** 参数选项（枚举类型） */
    options?: { value: any; label: string }[];
  }[];
  /** 模板生成函数 */
  generate: (parameters: any) => FacialAnimationPreset;
  /** 元数据 */
  metadata?: any;
}

/**
 * 面部动画预设系统
 */
export class FacialAnimationPresetSystem extends System {
  /** 系统名称 */
  static readonly NAME = 'FacialAnimationPresetSystem';

  /** 配置 */
  private config: FacialAnimationPresetSystemConfig;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 预设映射 */
  private presets: Map<string, FacialAnimationPreset> = new Map();

  /** 模板映射 */
  private templates: Map<string, FacialAnimationTemplate> = new Map();

  /** 活动预设映射 */
  private activePresets: Map<Entity, { presetId: string; startTime: number; duration: number; loop: boolean }> = new Map();

  /** 是否已初始化 */
  private initialized: boolean = false;

  /**
   * 构造函数
   * @param world 世界
   * @param config 配置
   */
  constructor(world: World, config: FacialAnimationPresetSystemConfig = {}) {
    super(world);

    this.config = {
      debug: false,
      autoLoadPresets: true,
      presetsPath: 'presets/facial-animation',
      useLocalStorage: true,
      ...config
    };

    if (this.config.debug) {
      console.log('面部动画预设系统初始化');
    }

    // 初始化预设
    if (this.config.autoLoadPresets) {
      this.loadPresets();
    }

    // 初始化默认模板
    this.initDefaultTemplates();

    this.initialized = true;
  }

  /**
   * 加载预设
   */
  private async loadPresets(): Promise<void> {
    try {
      // 从本地存储加载
      if (this.config.useLocalStorage) {
        this.loadPresetsFromLocalStorage();
      }

      // 从服务器加载
      await this.loadPresetsFromServer();

      if (this.config.debug) {
        console.log(`已加载 ${this.presets.size} 个预设`);
      }
    } catch (error) {
      console.error('加载预设失败:', error);
    }
  }

  /**
   * 从本地存储加载预设
   */
  private loadPresetsFromLocalStorage(): void {
    try {
      // 获取本地存储中的预设
      const presetsJson = localStorage.getItem('facialAnimationPresets');
      if (!presetsJson) return;

      // 解析预设
      const presets = JSON.parse(presetsJson) as FacialAnimationPreset[];

      // 添加预设
      for (const preset of presets) {
        this.presets.set(preset.id, preset);
      }

      if (this.config.debug) {
        console.log(`从本地存储加载了 ${presets.length} 个预设`);
      }
    } catch (error) {
      console.error('从本地存储加载预设失败:', error);
    }
  }

  /**
   * 从服务器加载预设
   */
  private async loadPresetsFromServer(): Promise<void> {
    try {
      // 构建预设路径
      const presetsPath = this.config.presetsPath || 'presets/facial-animation';

      // 获取预设列表
      const response = await fetch(`${presetsPath}/index.json`);
      if (!response.ok) {
        throw new Error(`获取预设列表失败: ${response.statusText}`);
      }

      // 解析预设列表
      const presetList = await response.json() as { id: string; path: string }[];

      // 加载每个预设
      for (const item of presetList) {
        try {
          // 获取预设
          const presetResponse = await fetch(`${presetsPath}/${item.path}`);
          if (!presetResponse.ok) {
            console.warn(`获取预设 ${item.id} 失败: ${presetResponse.statusText}`);
            continue;
          }

          // 解析预设
          const preset = await presetResponse.json() as FacialAnimationPreset;

          // 添加预设
          this.presets.set(preset.id, preset);
        } catch (error) {
          console.warn(`加载预设 ${item.id} 失败:`, error);
        }
      }

      if (this.config.debug) {
        console.log(`从服务器加载了 ${presetList.length} 个预设`);
      }
    } catch (error) {
      console.error('从服务器加载预设失败:', error);
    }
  }

  /**
   * 初始化默认模板
   */
  private initDefaultTemplates(): void {
    // 添加默认模板
    this.addTemplate(this.createBasicExpressionTemplate());
    this.addTemplate(this.createTalkingTemplate());
    this.addTemplate(this.createEmotionalTalkingTemplate());
    this.addTemplate(this.createBlinkTemplate());
    this.addTemplate(this.createBreathingTemplate());
    this.addTemplate(this.createIdleTemplate());
  }

  /**
   * 创建基本表情模板
   * @returns 基本表情模板
   */
  private createBasicExpressionTemplate(): FacialAnimationTemplate {
    return {
      id: 'basic-expression',
      name: '基本表情',
      description: '基本面部表情模板',
      category: '表情',
      tags: ['表情', '基本'],
      parameters: [
        {
          id: 'expression',
          name: '表情',
          type: 'enum',
          defaultValue: FacialExpressionType.HAPPY,
          options: [
            { value: FacialExpressionType.NEUTRAL, label: '中性' },
            { value: FacialExpressionType.HAPPY, label: '开心' },
            { value: FacialExpressionType.SAD, label: '悲伤' },
            { value: FacialExpressionType.ANGRY, label: '愤怒' },
            { value: FacialExpressionType.SURPRISED, label: '惊讶' },
            { value: FacialExpressionType.FEARFUL, label: '恐惧' },
            { value: FacialExpressionType.DISGUSTED, label: '厌恶' },
            { value: FacialExpressionType.CONTEMPT, label: '鄙视' }
          ]
        },
        {
          id: 'intensity',
          name: '强度',
          type: 'number',
          defaultValue: 1.0,
          min: 0,
          max: 1,
          step: 0.01
        },
        {
          id: 'duration',
          name: '持续时间',
          type: 'number',
          defaultValue: 3.0,
          min: 0.1,
          max: 10,
          step: 0.1
        },
        {
          id: 'fadeIn',
          name: '淡入时间',
          type: 'number',
          defaultValue: 0.5,
          min: 0,
          max: 2,
          step: 0.1
        },
        {
          id: 'fadeOut',
          name: '淡出时间',
          type: 'number',
          defaultValue: 0.5,
          min: 0,
          max: 2,
          step: 0.1
        },
        {
          id: 'loop',
          name: '循环',
          type: 'boolean',
          defaultValue: false
        }
      ],
      generate: (parameters) => {
        const expression = parameters.expression || FacialExpressionType.HAPPY;
        const intensity = parameters.intensity || 1.0;
        const duration = parameters.duration || 3.0;
        const fadeIn = parameters.fadeIn || 0.5;
        const fadeOut = parameters.fadeOut || 0.5;
        const loop = parameters.loop || false;

        // 创建关键帧
        const keyframes: FacialAnimationKeyframe[] = [
          {
            time: 0,
            expression,
            expressionWeight: 0
          },
          {
            time: fadeIn,
            expression,
            expressionWeight: intensity,
            easingType: 'easeOutQuad'
          },
          {
            time: duration - fadeOut,
            expression,
            expressionWeight: intensity
          },
          {
            time: duration,
            expression,
            expressionWeight: 0,
            easingType: 'easeInQuad'
          }
        ];

        return {
          id: `basic-expression-${expression}-${Date.now()}`,
          name: `基本表情 - ${expression}`,
          description: `基本表情预设 - ${expression}`,
          category: '表情',
          tags: ['表情', '基本', expression],
          duration,
          loop,
          keyframes
        };
      }
    };
  }

  /**
   * 创建说话模板
   * @returns 说话模板
   */
  private createTalkingTemplate(): FacialAnimationTemplate {
    return {
      id: 'talking',
      name: '说话',
      description: '说话动画模板',
      category: '对话',
      tags: ['对话', '说话', '口型'],
      parameters: [
        {
          id: 'duration',
          name: '持续时间',
          type: 'number',
          defaultValue: 5.0,
          min: 0.1,
          max: 30,
          step: 0.1
        },
        {
          id: 'intensity',
          name: '强度',
          type: 'number',
          defaultValue: 1.0,
          min: 0,
          max: 1,
          step: 0.01
        },
        {
          id: 'speed',
          name: '速度',
          type: 'number',
          defaultValue: 1.0,
          min: 0.5,
          max: 2,
          step: 0.1
        },
        {
          id: 'randomness',
          name: '随机性',
          type: 'number',
          defaultValue: 0.3,
          min: 0,
          max: 1,
          step: 0.01
        },
        {
          id: 'loop',
          name: '循环',
          type: 'boolean',
          defaultValue: true
        }
      ],
      generate: (parameters) => {
        const duration = parameters.duration || 5.0;
        const intensity = parameters.intensity || 1.0;
        const speed = parameters.speed || 1.0;
        const randomness = parameters.randomness || 0.3;
        const loop = parameters.loop || true;

        // 创建关键帧
        const keyframes: FacialAnimationKeyframe[] = [];

        // 添加初始关键帧
        keyframes.push({
          time: 0,
          viseme: VisemeType.SILENT,
          visemeWeight: 1.0
        });

        // 可能的口型序列
        const visemes = [
          VisemeType.AA,
          VisemeType.EE,
          VisemeType.IH,
          VisemeType.OH,
          VisemeType.OU,
          VisemeType.MM,
          VisemeType.SS,
          VisemeType.SILENT
        ];

        // 生成随机口型序列
        let time = 0.1;
        const syllableDuration = 0.2 / speed;
        const pauseDuration = 0.1 / speed;

        while (time < duration) {
          // 随机选择口型
          const viseme = visemes[Math.floor(Math.random() * (visemes.length - 1))];

          // 添加口型关键帧
          keyframes.push({
            time,
            viseme,
            visemeWeight: intensity * (1 - randomness * Math.random()),
            easingType: 'easeOutQuad'
          });

          // 更新时间
          time += syllableDuration;

          // 添加静默关键帧
          if (Math.random() < 0.3) {
            keyframes.push({
              time,
              viseme: VisemeType.SILENT,
              visemeWeight: 1.0,
              easingType: 'easeInQuad'
            });

            // 更新时间
            time += pauseDuration;
          }
        }

        // 添加结束关键帧
        keyframes.push({
          time: duration,
          viseme: VisemeType.SILENT,
          visemeWeight: 1.0,
          easingType: 'easeInQuad'
        });

        return {
          id: `talking-${Date.now()}`,
          name: '说话动画',
          description: '随机说话动画预设',
          category: '对话',
          tags: ['对话', '说话', '口型'],
          duration,
          loop,
          keyframes
        };
      }
    };
  }

  /**
   * 创建情感说话模板
   * @returns 情感说话模板
   */
  private createEmotionalTalkingTemplate(): FacialAnimationTemplate {
    return {
      id: 'emotional-talking',
      name: '情感说话',
      description: '带情感的说话动画模板',
      category: '对话',
      tags: ['对话', '说话', '情感', '口型'],
      parameters: [
        {
          id: 'expression',
          name: '表情',
          type: 'enum',
          defaultValue: FacialExpressionType.HAPPY,
          options: [
            { value: FacialExpressionType.NEUTRAL, label: '中性' },
            { value: FacialExpressionType.HAPPY, label: '开心' },
            { value: FacialExpressionType.SAD, label: '悲伤' },
            { value: FacialExpressionType.ANGRY, label: '愤怒' },
            { value: FacialExpressionType.SURPRISED, label: '惊讶' },
            { value: FacialExpressionType.FEARFUL, label: '恐惧' },
            { value: FacialExpressionType.DISGUSTED, label: '厌恶' },
            { value: FacialExpressionType.CONTEMPT, label: '鄙视' }
          ]
        },
        {
          id: 'expressionIntensity',
          name: '表情强度',
          type: 'number',
          defaultValue: 0.7,
          min: 0,
          max: 1,
          step: 0.01
        },
        {
          id: 'duration',
          name: '持续时间',
          type: 'number',
          defaultValue: 5.0,
          min: 0.1,
          max: 30,
          step: 0.1
        },
        {
          id: 'visemeIntensity',
          name: '口型强度',
          type: 'number',
          defaultValue: 1.0,
          min: 0,
          max: 1,
          step: 0.01
        },
        {
          id: 'speed',
          name: '速度',
          type: 'number',
          defaultValue: 1.0,
          min: 0.5,
          max: 2,
          step: 0.1
        },
        {
          id: 'randomness',
          name: '随机性',
          type: 'number',
          defaultValue: 0.3,
          min: 0,
          max: 1,
          step: 0.01
        },
        {
          id: 'loop',
          name: '循环',
          type: 'boolean',
          defaultValue: true
        }
      ],
      generate: (parameters) => {
        const expression = parameters.expression || FacialExpressionType.HAPPY;
        const expressionIntensity = parameters.expressionIntensity || 0.7;
        const duration = parameters.duration || 5.0;
        const visemeIntensity = parameters.visemeIntensity || 1.0;
        const speed = parameters.speed || 1.0;
        const randomness = parameters.randomness || 0.3;
        const loop = parameters.loop || true;

        // 创建关键帧
        const keyframes: FacialAnimationKeyframe[] = [];

        // 添加表情关键帧
        keyframes.push({
          time: 0,
          expression,
          expressionWeight: 0
        });

        keyframes.push({
          time: 0.5,
          expression,
          expressionWeight: expressionIntensity,
          easingType: 'easeOutQuad'
        });

        keyframes.push({
          time: duration - 0.5,
          expression,
          expressionWeight: expressionIntensity
        });

        keyframes.push({
          time: duration,
          expression,
          expressionWeight: 0,
          easingType: 'easeInQuad'
        });

        // 添加初始口型关键帧
        keyframes.push({
          time: 0,
          viseme: VisemeType.SILENT,
          visemeWeight: 1.0
        });

        // 可能的口型序列
        const visemes = [
          VisemeType.AA,
          VisemeType.EE,
          VisemeType.IH,
          VisemeType.OH,
          VisemeType.OU,
          VisemeType.MM,
          VisemeType.SS,
          VisemeType.SILENT
        ];

        // 生成随机口型序列
        let time = 0.1;
        const syllableDuration = 0.2 / speed;
        const pauseDuration = 0.1 / speed;

        while (time < duration) {
          // 随机选择口型
          const viseme = visemes[Math.floor(Math.random() * (visemes.length - 1))];

          // 添加口型关键帧
          keyframes.push({
            time,
            viseme,
            visemeWeight: visemeIntensity * (1 - randomness * Math.random()),
            easingType: 'easeOutQuad'
          });

          // 更新时间
          time += syllableDuration;

          // 添加静默关键帧
          if (Math.random() < 0.3) {
            keyframes.push({
              time,
              viseme: VisemeType.SILENT,
              visemeWeight: 1.0,
              easingType: 'easeInQuad'
            });

            // 更新时间
            time += pauseDuration;
          }
        }

        // 添加结束口型关键帧
        keyframes.push({
          time: duration,
          viseme: VisemeType.SILENT,
          visemeWeight: 1.0,
          easingType: 'easeInQuad'
        });

        return {
          id: `emotional-talking-${expression}-${Date.now()}`,
          name: `情感说话 - ${expression}`,
          description: `带${expression}情感的说话动画预设`,
          category: '对话',
          tags: ['对话', '说话', '情感', '口型', expression],
          duration,
          loop,
          keyframes
        };
      }
    };
  }

  /**
   * 创建眨眼模板
   * @returns 眨眼模板
   */
  private createBlinkTemplate(): FacialAnimationTemplate {
    return {
      id: 'blink',
      name: '眨眼',
      description: '眨眼动画模板',
      category: '面部',
      tags: ['眨眼', '面部'],
      parameters: [
        {
          id: 'duration',
          name: '持续时间',
          type: 'number',
          defaultValue: 10.0,
          min: 1,
          max: 30,
          step: 0.1
        },
        {
          id: 'blinkDuration',
          name: '眨眼持续时间',
          type: 'number',
          defaultValue: 0.2,
          min: 0.1,
          max: 0.5,
          step: 0.01
        },
        {
          id: 'minInterval',
          name: '最小间隔',
          type: 'number',
          defaultValue: 2.0,
          min: 0.5,
          max: 10,
          step: 0.1
        },
        {
          id: 'maxInterval',
          name: '最大间隔',
          type: 'number',
          defaultValue: 5.0,
          min: 1,
          max: 15,
          step: 0.1
        },
        {
          id: 'loop',
          name: '循环',
          type: 'boolean',
          defaultValue: true
        }
      ],
      generate: (parameters) => {
        const duration = parameters.duration || 10.0;
        const blinkDuration = parameters.blinkDuration || 0.2;
        const minInterval = parameters.minInterval || 2.0;
        const maxInterval = parameters.maxInterval || 5.0;
        const loop = parameters.loop || true;

        // 创建关键帧
        const keyframes: FacialAnimationKeyframe[] = [];

        // 生成眨眼关键帧
        let time = 0;
        while (time < duration) {
          // 添加眨眼开始关键帧
          keyframes.push({
            time,
            blendShapeWeights: { 'eyeBlink': 0 }
          });

          // 添加眨眼中间关键帧
          keyframes.push({
            time: time + blinkDuration / 2,
            blendShapeWeights: { 'eyeBlink': 1 },
            easingType: 'easeInOutQuad'
          });

          // 添加眨眼结束关键帧
          keyframes.push({
            time: time + blinkDuration,
            blendShapeWeights: { 'eyeBlink': 0 },
            easingType: 'easeInOutQuad'
          });

          // 更新时间
          time += blinkDuration + minInterval + Math.random() * (maxInterval - minInterval);
        }

        return {
          id: `blink-${Date.now()}`,
          name: '眨眼动画',
          description: '自然眨眼动画预设',
          category: '面部',
          tags: ['眨眼', '面部'],
          duration,
          loop,
          keyframes
        };
      }
    };
  }

  /**
   * 创建呼吸模板
   * @returns 呼吸模板
   */
  private createBreathingTemplate(): FacialAnimationTemplate {
    return {
      id: 'breathing',
      name: '呼吸',
      description: '呼吸动画模板',
      category: '面部',
      tags: ['呼吸', '面部'],
      parameters: [
        {
          id: 'duration',
          name: '持续时间',
          type: 'number',
          defaultValue: 10.0,
          min: 1,
          max: 30,
          step: 0.1
        },
        {
          id: 'breathDuration',
          name: '呼吸周期',
          type: 'number',
          defaultValue: 4.0,
          min: 2,
          max: 8,
          step: 0.1
        },
        {
          id: 'intensity',
          name: '强度',
          type: 'number',
          defaultValue: 0.3,
          min: 0.1,
          max: 1,
          step: 0.01
        },
        {
          id: 'loop',
          name: '循环',
          type: 'boolean',
          defaultValue: true
        }
      ],
      generate: (parameters) => {
        const duration = parameters.duration || 10.0;
        const breathDuration = parameters.breathDuration || 4.0;
        const intensity = parameters.intensity || 0.3;
        const loop = parameters.loop || true;

        // 创建关键帧
        const keyframes: FacialAnimationKeyframe[] = [];

        // 生成呼吸关键帧
        const cycles = Math.ceil(duration / breathDuration);
        for (let i = 0; i < cycles; i++) {
          const cycleStart = i * breathDuration;

          // 吸气开始
          keyframes.push({
            time: cycleStart,
            blendShapeWeights: { 'chestExpand': 0 }
          });

          // 吸气结束
          keyframes.push({
            time: cycleStart + breathDuration / 2,
            blendShapeWeights: { 'chestExpand': intensity },
            easingType: 'easeInOutQuad'
          });

          // 呼气结束
          keyframes.push({
            time: cycleStart + breathDuration,
            blendShapeWeights: { 'chestExpand': 0 },
            easingType: 'easeInOutQuad'
          });
        }

        return {
          id: `breathing-${Date.now()}`,
          name: '呼吸动画',
          description: '自然呼吸动画预设',
          category: '面部',
          tags: ['呼吸', '面部'],
          duration,
          loop,
          keyframes
        };
      }
    };
  }

  /**
   * 创建待机模板
   * @returns 待机模板
   */
  private createIdleTemplate(): FacialAnimationTemplate {
    return {
      id: 'idle',
      name: '待机',
      description: '待机动画模板',
      category: '面部',
      tags: ['待机', '面部', '眨眼', '呼吸'],
      parameters: [
        {
          id: 'duration',
          name: '持续时间',
          type: 'number',
          defaultValue: 15.0,
          min: 5,
          max: 60,
          step: 0.1
        },
        {
          id: 'expression',
          name: '表情',
          type: 'enum',
          defaultValue: FacialExpressionType.NEUTRAL,
          options: [
            { value: FacialExpressionType.NEUTRAL, label: '中性' },
            { value: FacialExpressionType.HAPPY, label: '开心' },
            { value: FacialExpressionType.SAD, label: '悲伤' }
          ]
        },
        {
          id: 'expressionIntensity',
          name: '表情强度',
          type: 'number',
          defaultValue: 0.3,
          min: 0,
          max: 1,
          step: 0.01
        },
        {
          id: 'loop',
          name: '循环',
          type: 'boolean',
          defaultValue: true
        }
      ],
      generate: (parameters) => {
        const duration = parameters.duration || 15.0;
        const expression = parameters.expression || FacialExpressionType.NEUTRAL;
        const expressionIntensity = parameters.expressionIntensity || 0.3;
        const loop = parameters.loop || true;

        // 创建关键帧
        const keyframes: FacialAnimationKeyframe[] = [];

        // 添加表情关键帧
        keyframes.push({
          time: 0,
          expression,
          expressionWeight: 0
        });

        keyframes.push({
          time: 1.0,
          expression,
          expressionWeight: expressionIntensity,
          easingType: 'easeOutQuad'
        });

        keyframes.push({
          time: duration - 1.0,
          expression,
          expressionWeight: expressionIntensity
        });

        keyframes.push({
          time: duration,
          expression,
          expressionWeight: 0,
          easingType: 'easeInQuad'
        });

        // 添加眨眼关键帧
        const blinkDuration = 0.2;
        let blinkTime = 2.0;
        while (blinkTime < duration) {
          // 添加眨眼开始关键帧
          keyframes.push({
            time: blinkTime,
            blendShapeWeights: { 'eyeBlink': 0 }
          });

          // 添加眨眼中间关键帧
          keyframes.push({
            time: blinkTime + blinkDuration / 2,
            blendShapeWeights: { 'eyeBlink': 1 },
            easingType: 'easeInOutQuad'
          });

          // 添加眨眼结束关键帧
          keyframes.push({
            time: blinkTime + blinkDuration,
            blendShapeWeights: { 'eyeBlink': 0 },
            easingType: 'easeInOutQuad'
          });

          // 更新时间
          blinkTime += blinkDuration + 2.0 + Math.random() * 3.0;
        }

        // 添加呼吸关键帧
        const breathDuration = 4.0;
        const breathIntensity = 0.3;
        const cycles = Math.ceil(duration / breathDuration);
        for (let i = 0; i < cycles; i++) {
          const cycleStart = i * breathDuration;

          // 吸气开始
          keyframes.push({
            time: cycleStart,
            blendShapeWeights: { 'chestExpand': 0 }
          });

          // 吸气结束
          keyframes.push({
            time: cycleStart + breathDuration / 2,
            blendShapeWeights: { 'chestExpand': breathIntensity },
            easingType: 'easeInOutQuad'
          });

          // 呼气结束
          keyframes.push({
            time: cycleStart + breathDuration,
            blendShapeWeights: { 'chestExpand': 0 },
            easingType: 'easeInOutQuad'
          });
        }

        // 添加微小的头部运动
        for (let time = 0; time < duration; time += 5) {
          const headTilt = (Math.random() - 0.5) * 0.1;
          const headTurn = (Math.random() - 0.5) * 0.1;

          keyframes.push({
            time,
            blendShapeWeights: { 'headTilt': 0, 'headTurn': 0 }
          });

          keyframes.push({
            time: time + 2.5,
            blendShapeWeights: { 'headTilt': headTilt, 'headTurn': headTurn },
            easingType: 'easeInOutQuad'
          });

          keyframes.push({
            time: time + 5,
            blendShapeWeights: { 'headTilt': 0, 'headTurn': 0 },
            easingType: 'easeInOutQuad'
          });
        }

        return {
          id: `idle-${expression}-${Date.now()}`,
          name: `待机动画 - ${expression}`,
          description: `带${expression}表情的待机动画预设`,
          category: '面部',
          tags: ['待机', '面部', '眨眼', '呼吸', expression],
          duration,
          loop,
          keyframes
        };
      }
    };
  }
}
